using System.Windows;
using WPF_MVVM_Test.MVVM_Model;
using WPF_MVVM_Test.MVVM_Model.Plan;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;

namespace WPF_MVVM_Test.MVVM_View.Dialog
{
    public partial class SelectProductDialog : Window
    {
        public ProductModel? SelectedProduct
        {
            get
            {
                if (DataContext is SelectProductViewModel viewModel)
                {
                    return viewModel.SelectedProduct;
                }
                return null;
            }
        }

        public SelectProductDialog()
        {
            InitializeComponent();
            DataContext = new SelectProductViewModel();
        }
    }
}



