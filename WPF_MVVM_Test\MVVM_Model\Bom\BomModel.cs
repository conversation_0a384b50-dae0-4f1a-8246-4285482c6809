using System;
using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.Bom.Bom
{
    /// <summary>
    /// BOM数据项模型
    /// </summary>
    public class BomItem
    {
        public string Id { get; set; }
        public string BomNumber { get; set; }
        public bool IsSystemNumber { get; set; }
        public bool IsDefault { get; set; }
        public string Version { get; set; }
        public string ProductId { get; set; }  // GUID字符串类型
        public string ProductName { get; set; }
        public string ColorCode { get; set; }
        public string Unit { get; set; }
        public decimal DailyOutput { get; set; }
        public string Remark { get; set; }
        public string ProcessRouteId { get; set; }
        public string ProcessRouteName { get; set; }
        public DateTime CreatedTime { get; set; }
        public DateTime LastUpdatedTime { get; set; }
        public int ItemCount { get; set; }
    }

    /// <summary>
    /// BOM分页数据模型
    /// </summary>
    public class BomPagedData
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<BomItem> Data { get; set; } = new List<BomItem>();
    }

    /// <summary>
    /// API响应模型
    /// </summary>
    public class BomApiResponse
    {
        public BomPagedData Data { get; set; }
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; }
    }

    /// <summary>
    /// BOM查询参数模型
    /// </summary>
    public class BomQueryParams
    {
        public string ProductName { get; set; } = "";
        public string BomNumber { get; set; } = "";
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}