using System;
using System.Threading.Tasks;

namespace WPF_MVVM_Test
{
    /// <summary>
    /// 控制台测试程序入口
    /// </summary>
    public class Program
    {
        /// <summary>
        /// 控制台程序入口点
        /// </summary>
        /// <param name="args"></param>
        /// <returns></returns>
        public static async Task Main(string[] args)
        {
            Console.WriteLine("=== WPF_MVVM_Test API连接测试 ===");
            Console.WriteLine();

            try
            {
                // 运行API连接测试
                await TestApiConnection.RunAllTestsAsync();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"异常详情: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
