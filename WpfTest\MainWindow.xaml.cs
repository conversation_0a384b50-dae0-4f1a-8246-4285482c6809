﻿using System.ComponentModel;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Windows;

namespace WpfTest
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window, INotifyPropertyChanged
    {
        // 这个事件是双向绑定的关键！
        public event PropertyChangedEventHandler? PropertyChanged;

        // 私有字段存储实际值
        private string _username = "";
        private string _password = "";

        // 公共属性，界面会绑定到这里
        public string Username
        {
            get { return _username; }
            set
            {
                _username = value;
                // 通知界面：这个属性变了,请更新显示！
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs("Username"));
            }
        }

        public string Password
        {
            get { return _password; }
            set
            {
                _password = value;
                // 通知界面：这个属性变了，请更新显示！
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs("Password"));
            }
        }

        public MainWindow()
        {
            InitializeComponent();
            // 告诉界面：绑定到我这个窗口的属性
            DataContext = this;
        }

        // 登录按钮点击事件
        private async void LoginButton_Click(object sender, RoutedEventArgs e)
        {
            // 第一步：验证输入
            if (string.IsNullOrWhiteSpace(Username))
            {
                System.Windows.MessageBox.Show("请输入用户名！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(Password))
            {
                System.Windows.MessageBox.Show("请输入密码！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // 第二步：禁用按钮，显示加载状态
                // 使用sender参数获取点击的按钮
                var button = LoginButton;
                button.IsEnabled = false;
                button.Content = "登录中...";

                // 第三步：调用登录方法
                bool loginSuccess = await DoLoginAsync(Username, Password);

                // 第四步：处理登录结果
                if (loginSuccess)
                {
                    System.Windows.MessageBox.Show("登录成功！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    // 这里可以跳转到主页面或关闭登录窗口
                }
                else
                {
                    System.Windows.MessageBox.Show("登录失败，请检查用户名和密码！", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                // 第五步：处理异常
                System.Windows.MessageBox.Show($"登录时发生错误：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // 第六步：恢复按钮状态
                var button = LoginButton;
                button.IsEnabled = true;
                button.Content = "登录";
            }
        }

        /// <summary>
        /// 执行登录的异步方法 - 这是核心的HTTP请求部分
        /// </summary>
        /// <param name="username">用户名</param>
        /// <param name="password">密码</param>
        /// <returns>登录是否成功</returns>
        private async Task<bool> DoLoginAsync(string username, string password)
        {
            // 创建HttpClient - 用于发送HTTP请求
            using var httpClient = new HttpClient();

            try
            {
                // 第一步：准备要发送的数据（匹配你的API格式）
                var loginData = new
                {
                    usernameOrEmail = username,    // 用户名或邮箱
                    password = password,           // 密码
                    rememberMe = true,            // 记住我
                    tenantId = "string"           // 租户ID（根据你的API要求）
                };

                // 第二步：将数据转换为JSON格式
                string jsonString = JsonSerializer.Serialize(loginData);

                // 第三步：创建HTTP请求内容
                var content = new StringContent(
                    jsonString,                    // JSON字符串
                    Encoding.UTF8,                 // 编码格式
                    "application/json"             // 内容类型
                );

                // 第四步：发送POST请求到你的API
                var response = await httpClient.PostAsync("http://localhost:5143/api/Auth/login", content);

                // 第五步：检查响应状态
                if (response.IsSuccessStatusCode)
                {
                    // 登录成功，读取返回的内容
                    string responseContent = await response.Content.ReadAsStringAsync();

                    // 显示服务器返回的详细信息（用于调试）
                    System.Windows.MessageBox.Show($"服务器返回：{responseContent}", "调试信息", MessageBoxButton.OK, MessageBoxImage.Information);

                    // 这里可以解析返回的Token等信息
                    // 例如：var result = JsonSerializer.Deserialize<LoginResult>(responseContent);

                    return true;  // 返回成功
                }
                else
                {
                    // 登录失败，读取错误信息
                    string errorContent = await response.Content.ReadAsStringAsync();

                    // 显示详细的错误信息
                    System.Windows.MessageBox.Show($"登录失败！\n状态码：{response.StatusCode}\n错误信息：{errorContent}",
                                  "登录失败", MessageBoxButton.OK, MessageBoxImage.Error);

                    return false; // 返回失败
                }
            }
            catch (HttpRequestException ex)
            {
                // 网络请求异常（比如服务器无法连接）
                throw new Exception($"网络请求失败：{ex.Message}");
            }
            catch (TaskCanceledException ex)
            {
                // 请求超时
                throw new Exception("请求超时，请检查网络连接");
            }
            catch (Exception ex)
            {
                // 其他异常
                throw new Exception($"登录过程中发生未知错误：{ex.Message}");
            }
        }

        // 测试按钮：演示代码修改属性时的区别
        private void TestButton_Click(object sender, RoutedEventArgs e)
        {
            // 代码中修改属性值
            Username = "admin";
            Password = "123456";

            // 如果没有PropertyChanged通知，界面不会更新！
            // 有了PropertyChanged通知，界面会自动更新
        }

    }
}