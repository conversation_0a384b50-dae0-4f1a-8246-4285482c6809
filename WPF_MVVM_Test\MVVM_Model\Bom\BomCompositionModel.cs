using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Bom
{
    /// <summary>
    /// BOM组成项模型
    /// </summary>
    public class BomCompositionItem : INotifyPropertyChanged
    {
        private int _sequence;
        private string _productName = string.Empty;
        private string _productNumber = string.Empty;
        private string _specification = string.Empty;
        private string _unit = string.Empty;
        private string _bomNumber = string.Empty;
        private string _bomVersion = string.Empty;
        private decimal _usageQuantity;
        private string _usageRatio = string.Empty;
        private bool _isExpanded;
        private bool _isExpandable;
        private List<BomCompositionItem> _children = new List<BomCompositionItem>();

        /// <summary>
        /// 序号
        /// </summary>
        [JsonPropertyName("sequence")]
        public int Sequence
        {
            get => _sequence;
            set
            {
                if (_sequence != value)
                {
                    _sequence = value;
                    OnPropertyChanged(nameof(Sequence));
                }
            }
        }

        /// <summary>
        /// 产品名称
        /// </summary>
        [JsonPropertyName("productName")]
        public string ProductName
        {
            get => _productName;
            set
            {
                if (_productName != value)
                {
                    _productName = value;
                    OnPropertyChanged(nameof(ProductName));
                }
            }
        }

        /// <summary>
        /// 产品编号
        /// </summary>
        [JsonPropertyName("productNumber")]
        public string ProductNumber
        {
            get => _productNumber;
            set
            {
                if (_productNumber != value)
                {
                    _productNumber = value;
                    OnPropertyChanged(nameof(ProductNumber));
                }
            }
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonPropertyName("specification")]
        public string Specification
        {
            get => _specification;
            set
            {
                if (_specification != value)
                {
                    _specification = value;
                    OnPropertyChanged(nameof(Specification));
                }
            }
        }

        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }
        }

        /// <summary>
        /// BOM编号
        /// </summary>
        [JsonPropertyName("bomNumber")]
        public string BomNumber
        {
            get => _bomNumber;
            set
            {
                if (_bomNumber != value)
                {
                    _bomNumber = value;
                    OnPropertyChanged(nameof(BomNumber));
                    OnPropertyChanged(nameof(HasSubBom));
                }
            }
        }

        /// <summary>
        /// BOM版本
        /// </summary>
        [JsonPropertyName("bomVersion")]
        public string BomVersion
        {
            get => _bomVersion;
            set
            {
                if (_bomVersion != value)
                {
                    _bomVersion = value;
                    OnPropertyChanged(nameof(BomVersion));
                }
            }
        }

        /// <summary>
        /// 使用量
        /// </summary>
        [JsonPropertyName("usageQuantity")]
        public decimal UsageQuantity
        {
            get => _usageQuantity;
            set
            {
                if (_usageQuantity != value)
                {
                    _usageQuantity = value;
                    OnPropertyChanged(nameof(UsageQuantity));
                }
            }
        }

        /// <summary>
        /// 使用比例
        /// </summary>
        [JsonPropertyName("usageRatio")]
        public string UsageRatio
        {
            get => _usageRatio;
            set
            {
                if (_usageRatio != value)
                {
                    _usageRatio = value;
                    OnPropertyChanged(nameof(UsageRatio));
                }
            }
        }

        /// <summary>
        /// 是否可展开
        /// </summary>
        [JsonPropertyName("isExpandable")]
        public bool IsExpandable
        {
            get => _isExpandable;
            set
            {
                if (_isExpandable != value)
                {
                    _isExpandable = value;
                    OnPropertyChanged(nameof(IsExpandable));
                    OnPropertyChanged(nameof(HasSubBom));
                }
            }
        }

        /// <summary>
        /// 子项列表
        /// </summary>
        [JsonPropertyName("children")]
        public List<BomCompositionItem> Children
        {
            get => _children;
            set
            {
                if (_children != value)
                {
                    _children = value ?? new List<BomCompositionItem>();
                    OnPropertyChanged(nameof(Children));
                    OnPropertyChanged(nameof(HasSubBom));
                }
            }
        }

        /// <summary>
        /// 是否展开（用于树形结构）
        /// </summary>
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                if (_isExpanded != value)
                {
                    _isExpanded = value;
                    OnPropertyChanged(nameof(IsExpanded));
                }
            }
        }

        /// <summary>
        /// 是否有子BOM
        /// </summary>
        public bool HasSubBom => IsExpandable || (Children != null && Children.Count > 0);

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 获取测试数据
        /// </summary>
        /// <returns></returns>
        public static ObservableCollection<BomCompositionItem> GetTestData()
        {
            return new ObservableCollection<BomCompositionItem>
            {
                new BomCompositionItem
                {
                    Sequence = 1,
                    ProductName = "原料预处理",
                    ProductNumber = "PS20241201001",
                    Specification = "工序",
                    Unit = "个",
                    BomNumber = "BOM2025073084901",
                    BomVersion = "v3.0",
                    UsageQuantity = 1,
                    UsageRatio = "100%",
                    IsExpandable = true,
                    Children = new List<BomCompositionItem>
                    {
                        new BomCompositionItem
                        {
                            Sequence = 1,
                            ProductName = "原料清洗",
                            ProductNumber = "ZZ20250730001",
                            Specification = "工序",
                            Unit = "个",
                            BomNumber = "BOM20250728154601",
                            BomVersion = "1.0",
                            UsageQuantity = 1,
                            UsageRatio = "100%",
                            IsExpandable = false,
                            Children = new List<BomCompositionItem>()
                        },
                        new BomCompositionItem
                        {
                            Sequence = 2,
                            ProductName = "主加工",
                            ProductNumber = "ZZ20250730002",
                            Specification = "工序",
                            Unit = "个",
                            BomNumber = "BOM20250728154601",
                            BomVersion = "1.0",
                            UsageQuantity = 1,
                            UsageRatio = "100%",
                            IsExpandable = false,
                            Children = new List<BomCompositionItem>()
                        },
                        new BomCompositionItem
                        {
                            Sequence = 3,
                            ProductName = "质量检验",
                            ProductNumber = "ZZ20250730003",
                            Specification = "工序",
                            Unit = "个",
                            BomNumber = "BOM20250728154601",
                            BomVersion = "1.0",
                            UsageQuantity = 1,
                            UsageRatio = "100%",
                            IsExpandable = false,
                            Children = new List<BomCompositionItem>()
                        }
                    }
                },
                new BomCompositionItem
                {
                    Sequence = 2,
                    ProductName = "主加工",
                    ProductNumber = "PS20241201002",
                    Specification = "工序",
                    Unit = "个",
                    BomNumber = "BOM2025073084901",
                    BomVersion = "v3.0",
                    UsageQuantity = 1,
                    UsageRatio = "100%",
                    IsExpandable = false,
                    Children = new List<BomCompositionItem>()
                },
                new BomCompositionItem
                {
                    Sequence = 3,
                    ProductName = "精加工",
                    ProductNumber = "PS20241201003",
                    Specification = "工序",
                    Unit = "个",
                    BomNumber = "BOM2025073084901",
                    BomVersion = "v3.0",
                    UsageQuantity = 1,
                    UsageRatio = "100%",
                    IsExpandable = false,
                    Children = new List<BomCompositionItem>()
                },
                new BomCompositionItem
                {
                    Sequence = 4,
                    ProductName = "检验",
                    ProductNumber = "PS20241201004",
                    Specification = "工序",
                    Unit = "个",
                    BomNumber = "BOM2025073084901",
                    BomVersion = "v3.0",
                    UsageQuantity = 1,
                    UsageRatio = "100%",
                    IsExpandable = false,
                    Children = new List<BomCompositionItem>()
                },
                new BomCompositionItem
                {
                    Sequence = 5,
                    ProductName = "包装",
                    ProductNumber = "PS20241201005",
                    Specification = "工序",
                    Unit = "个",
                    BomNumber = "BOM2025073084901",
                    BomVersion = "v3.0",
                    UsageQuantity = 1,
                    UsageRatio = "100%",
                    IsExpandable = false,
                    Children = new List<BomCompositionItem>()
                }
            };
        }
    }

    /// <summary>
    /// BOM组成API响应模型
    /// </summary>
    public class BomCompositionApiResponse
    {
        [JsonPropertyName("data")]
        public List<BomCompositionItem> Data { get; set; } = new List<BomCompositionItem>();

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }
}