# 🏭 生产工单排产功能说明

## ✨ 功能概述

在生产工单页面，用户可以点击"排产"按钮为待排产的工单进行排产操作。系统会弹出一个排产对话框，显示工单的基础信息并允许用户编辑排产相关的参数。

## 🎯 功能特点

### 📋 基础信息展示（只读）
- **工单编号**：显示工单的唯一编号
- **工单名称**：显示工单对应的产品名称
- **产品编号**：显示产品的编号
- **规格型号**：显示产品的规格型号
- **单位**：显示产品的计量单位
- **BOM编码**：显示关联的BOM编码

### ⚙️ 排产信息编辑（可修改）
- **计划数量**：可以修改生产数量
- **开工时间**：可以选择计划开工日期
- **完工时间**：可以选择计划完工日期
- **备注**：可以添加排产相关的备注信息

## 🔧 技术实现

### 文件结构
```
WPF_MVVM_Test/
├── MVVM_View/ProductionOrder/
│   ├── ScheduleProductionDialog.xaml          # 排产对话框界面
│   └── ScheduleProductionDialog.xaml.cs       # 对话框代码后置
├── MVVM_ViewModel/ProductionOrder/
│   ├── ScheduleProductionDialogViewModel.cs   # 排产对话框ViewModel
│   └── ProductionOrderViewModel.cs            # 生产工单主ViewModel
└── MVVM_Services/
    └── ProductionOrderService.cs              # 生产工单服务
```

### 核心类说明

#### 1. ScheduleProductionDialogViewModel
- **职责**：管理排产对话框的数据和业务逻辑
- **主要功能**：
  - 数据反填：从原工单数据反填基础信息
  - 数据验证：验证用户输入的排产参数
  - API调用：调用后端排产接口
  - 对话框控制：管理对话框的显示和关闭

#### 2. ScheduleProductionDialog.xaml
- **职责**：排产对话框的用户界面
- **设计特点**：
  - 分区域显示：基础信息区域（灰色背景，只读）和排产信息区域（绿色背景，可编辑）
  - 现代化样式：使用Material Design风格的按钮和输入框
  - 响应式布局：适配不同屏幕尺寸

#### 3. ProductionOrderService
- **新增方法**：`ScheduleProductionAsync(object request)`
- **功能**：调用后端API进行排产操作

## 🚀 使用流程

### 1. 打开排产对话框
1. 在生产工单列表中找到状态为"待排产"的工单
2. 点击操作列中的"排产"链接
3. 系统弹出排产对话框

### 2. 查看基础信息
- 对话框上半部分显示工单的基础信息
- 这些信息为只读状态，不可修改
- 信息包括：工单编号、工单名称、产品编号、规格型号、单位、BOM编码

### 3. 编辑排产信息
- 对话框下半部分为排产信息编辑区域
- **计划数量**：默认显示原工单数量，可修改
- **开工时间**：默认为今天，可选择其他日期
- **完工时间**：默认为7天后，可选择其他日期
- **备注**：可添加排产相关说明

### 4. 数据验证
系统会自动验证以下内容：
- 计划数量必须大于0
- 开工时间不能为空
- 完工时间不能早于开工时间

### 5. 确认排产
- 点击"✅ 确定排产"按钮提交排产信息
- 系统调用后端API进行排产
- 排产成功后显示成功提示并关闭对话框
- 自动刷新生产工单列表

### 6. 取消操作
- 点击"❌ 取消"按钮可以取消排产操作
- 直接关闭对话框，不保存任何修改

## 🎨 界面设计

### 视觉特点
- **分区明确**：基础信息区域使用灰色背景，排产信息区域使用绿色背景
- **图标提示**：使用emoji图标增强用户体验
- **现代化按钮**：主要操作按钮使用蓝色，次要操作使用白色边框
- **输入框样式**：统一的圆角边框和内边距

### 响应式设计
- 对话框大小：650x500像素
- 自适应内容布局
- 支持键盘导航

## 🔍 测试要点

### 功能测试
1. **对话框打开**：点击排产按钮能正常弹出对话框
2. **数据反填**：基础信息正确显示且不可编辑
3. **数据编辑**：排产信息可以正常编辑
4. **数据验证**：输入无效数据时显示相应提示
5. **排产操作**：点击确定后能正常调用API
6. **列表刷新**：排产成功后工单列表自动刷新

### 异常测试
1. **网络异常**：API调用失败时的错误处理
2. **数据异常**：传入空数据或异常数据的处理
3. **界面异常**：对话框显示异常的处理

## 📝 注意事项

1. **权限控制**：只有状态为"待排产"的工单才显示排产按钮
2. **数据一致性**：排产成功后需要刷新列表以保持数据一致性
3. **用户体验**：操作过程中提供清晰的反馈信息
4. **错误处理**：所有异常情况都有相应的错误提示

## 🎉 功能完成状态

✅ **已完成功能**：
- 排产对话框界面设计
- 数据反填和验证逻辑
- API调用和错误处理
- 与主页面的集成
- 用户交互和反馈

🚀 **功能已就绪，可以开始测试使用！**
