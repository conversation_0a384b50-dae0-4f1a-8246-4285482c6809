using System;
using WPF_MVVM_Test.MVVM_Model.Bom;

namespace WPF_MVVM_Test.MVVM_Model
{
    /// <summary>
    /// 产品模型
    /// </summary>
    public class ProductModel
    {
        /// <summary>
        /// 序号（用于显示）
        /// </summary>
        public int Index { get; set; }

        /// <summary>
        /// 产品ID
        /// </summary>
        public string Id { get; set; } = string.Empty;

        /// <summary>
        /// 产品编号
        /// </summary>
        public string MaterialNumber { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称
        /// </summary>
        public string MaterialName { get; set; } = string.Empty;

        /// <summary>
        /// 规格型号
        /// </summary>
        public string SpecificationModel { get; set; } = string.Empty;

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 产品类型
        /// </summary>
        public string? MaterialType { get; set; }

        /// <summary>
        /// 产品属性
        /// </summary>
        public string MaterialProperty { get; set; } = string.Empty;

        /// <summary>
        /// 产品分类ID
        /// </summary>
        public string MaterialCategoryId { get; set; } = string.Empty;

        /// <summary>
        /// 状态
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime EffectiveDate { get; set; }

        /// <summary>
        /// 生效单位
        /// </summary>
        public string EffectiveUnit { get; set; } = string.Empty;

        /// <summary>
        /// 预警天数
        /// </summary>
        public int AlarmDays { get; set; }

        /// <summary>
        /// 库存上限
        /// </summary>
        public decimal StockUpperLimit { get; set; }

        /// <summary>
        /// 库存下限
        /// </summary>
        public decimal StockLowerLimit { get; set; }

        /// <summary>
        /// 采购价格
        /// </summary>
        public decimal PurchasePrice { get; set; }

        /// <summary>
        /// 销售价格
        /// </summary>
        public decimal SalesPrice { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 产品图片
        /// </summary>
        public string? MaterialImage { get; set; }

        /// <summary>
        /// 附件
        /// </summary>
        public string? Attachment { get; set; }
    }

    /// <summary>
    /// 产品API响应模型
    /// </summary>
    public class ProductApiResponse
    {
        public ProductDataWrapper Data { get; set; } = new();
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 产品数据包装器
    /// </summary>
    public class ProductDataWrapper
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<ProductModel> Data { get; set; } = new();
    }

    /// <summary>
    /// 产品API响应模型
    /// </summary>
    public class ProductApiResponses
    {
        public ProductDataWrappers Data { get; set; } = new();
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 产品数据包装器
    /// </summary>
    public class ProductDataWrappers
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<ProductEntity> Data { get; set; } = new();
    }
}