using System;
using System.ComponentModel;

namespace WPF_MVVM_Test.MVVM_Model.Material
{
    /// <summary>
    /// 物料信息模型
    /// </summary>
    public class Material : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _materialNumber = string.Empty;
        private string _materialName = string.Empty;
        private string _specificationModel = string.Empty;
        private string _unit = string.Empty;
        private int _materialType;
        private string _materialProperty = string.Empty;
        private string _materialCategoryId = string.Empty;
        private string _materialCategoryName = string.Empty;
        private string _status = string.Empty;
        private DateTime _effectiveDate = DateTime.Now;
        private string _effectiveUnit = string.Empty;
        private int _alarmDays = 365;
        private int _stockUpperLimit = 0;
        private int _stockLowerLimit = 0;
        private decimal _purchasePrice = 0;
        private decimal _salesPrice = 0;
        private string _remarks = string.Empty;
        private string _materialImage = string.Empty;
        private string _attachment = string.Empty;

        /// <summary>
        /// 物料ID
        /// </summary>
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber
        {
            get => _materialNumber;
            set
            {
                if (_materialNumber != value)
                {
                    _materialNumber = value;
                    OnPropertyChanged(nameof(MaterialNumber));
                }
            }
        }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName
        {
            get => _materialName;
            set
            {
                if (_materialName != value)
                {
                    _materialName = value;
                    OnPropertyChanged(nameof(MaterialName));
                }
            }
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string SpecificationModel
        {
            get => _specificationModel;
            set
            {
                if (_specificationModel != value)
                {
                    _specificationModel = value;
                    OnPropertyChanged(nameof(SpecificationModel));
                }
            }
        }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }
        }

        /// <summary>
        /// 物料类型
        /// </summary>
        public int MaterialType
        {
            get => _materialType;
            set
            {
                if (_materialType != value)
                {
                    _materialType = value;
                    OnPropertyChanged(nameof(MaterialType));
                }
            }
        }

        /// <summary>
        /// 物料属性
        /// </summary>
        public string MaterialProperty
        {
            get => _materialProperty;
            set
            {
                if (_materialProperty != value)
                {
                    _materialProperty = value;
                    OnPropertyChanged(nameof(MaterialProperty));
                }
            }
        }

        /// <summary>
        /// 物料分类ID
        /// </summary>
        public string MaterialCategoryId
        {
            get => _materialCategoryId;
            set
            {
                if (_materialCategoryId != value)
                {
                    _materialCategoryId = value;
                    OnPropertyChanged(nameof(MaterialCategoryId));
                }
            }
        }

        /// <summary>
        /// 物料分类名称
        /// </summary>
        public string MaterialCategoryName
        {
            get => _materialCategoryName;
            set
            {
                if (_materialCategoryName != value)
                {
                    _materialCategoryName = value;
                    OnPropertyChanged(nameof(MaterialCategoryName));
                }
            }
        }

        /// <summary>
        /// 状态（启用/停用）
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        /// <summary>
        /// 生效日期
        /// </summary>
        public DateTime EffectiveDate
        {
            get => _effectiveDate;
            set
            {
                if (_effectiveDate != value)
                {
                    _effectiveDate = value;
                    OnPropertyChanged(nameof(EffectiveDate));
                }
            }
        }

        /// <summary>
        /// 生效单位
        /// </summary>
        public string EffectiveUnit
        {
            get => _effectiveUnit;
            set
            {
                if (_effectiveUnit != value)
                {
                    _effectiveUnit = value;
                    OnPropertyChanged(nameof(EffectiveUnit));
                }
            }
        }

        /// <summary>
        /// 预警天数
        /// </summary>
        public int AlarmDays
        {
            get => _alarmDays;
            set
            {
                if (_alarmDays != value)
                {
                    _alarmDays = value;
                    OnPropertyChanged(nameof(AlarmDays));
                }
            }
        }

        /// <summary>
        /// 库存上限
        /// </summary>
        public int StockUpperLimit
        {
            get => _stockUpperLimit;
            set
            {
                if (_stockUpperLimit != value)
                {
                    _stockUpperLimit = value;
                    OnPropertyChanged(nameof(StockUpperLimit));
                }
            }
        }

        /// <summary>
        /// 库存下限
        /// </summary>
        public int StockLowerLimit
        {
            get => _stockLowerLimit;
            set
            {
                if (_stockLowerLimit != value)
                {
                    _stockLowerLimit = value;
                    OnPropertyChanged(nameof(StockLowerLimit));
                }
            }
        }

        /// <summary>
        /// 采购价格
        /// </summary>
        public decimal PurchasePrice
        {
            get => _purchasePrice;
            set
            {
                if (_purchasePrice != value)
                {
                    _purchasePrice = value;
                    OnPropertyChanged(nameof(PurchasePrice));
                }
            }
        }

        /// <summary>
        /// 销售价格
        /// </summary>
        public decimal SalesPrice
        {
            get => _salesPrice;
            set
            {
                if (_salesPrice != value)
                {
                    _salesPrice = value;
                    OnPropertyChanged(nameof(SalesPrice));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks
        {
            get => _remarks;
            set
            {
                if (_remarks != value)
                {
                    _remarks = value;
                    OnPropertyChanged(nameof(Remarks));
                }
            }
        }

        /// <summary>
        /// 物料图片
        /// </summary>
        public string MaterialImage
        {
            get => _materialImage;
            set
            {
                if (_materialImage != value)
                {
                    _materialImage = value;
                    OnPropertyChanged(nameof(MaterialImage));
                }
            }
        }

        /// <summary>
        /// 附件
        /// </summary>
        public string Attachment
        {
            get => _attachment;
            set
            {
                if (_attachment != value)
                {
                    _attachment = value;
                    OnPropertyChanged(nameof(Attachment));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}