<Window x:Class="WPF_MVVM_Test.MVVM_View.Dialog.SelectSalesOrderDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择销售订单" Height="600" Width="900"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize">
    
    <Window.Resources>
        <!-- 添加缺失的转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <Grid Grid.Row="0" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0" Text="销售编号：" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBox Grid.Column="1" Text="{Binding SearchSalesCode, UpdateSourceTrigger=PropertyChanged}" 
                     Height="35" Margin="0,0,15,0"/>

            <TextBlock Grid.Column="2" Text="销售名称：" VerticalAlignment="Center" Margin="0,0,10,0"/>
            <TextBox Grid.Column="3" Text="{Binding SearchSalesName, UpdateSourceTrigger=PropertyChanged}" 
                     Height="35" Margin="0,0,15,0"/>

            <Button Grid.Column="5" Content="搜索" 
                    Command="{Binding SearchCommand}"
                    Width="80" Height="35" Margin="5,0,0,0"/>
            
            <Button Grid.Column="6" Content="重置" 
                    Command="{Binding ResetCommand}"
                    Width="80" Height="35" Margin="5,0,0,0"/>
        </Grid>

        <!-- 加载提示 -->
        <TextBlock Grid.Row="1" Text="正在加载..." 
                   Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"
                   HorizontalAlignment="Center" Margin="0,0,0,10"/>

        <!-- 数据表格 -->
        <DataGrid Grid.Row="2" ItemsSource="{Binding SalesOrders}"
                  SelectedItem="{Binding SelectedOrder}"
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                  GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                  Background="White" RowBackground="White" AlternatingRowBackground="#FAFAFA"
                  BorderThickness="1" BorderBrush="#E0E0E0"
                  FontSize="12" SelectionMode="Single">
            
            <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60"/>
                <DataGridTextColumn Header="销售编号" Binding="{Binding SalesCode}" Width="120"/>
                <DataGridTextColumn Header="销售名称" Binding="{Binding SalesName}" Width="150"/>
                <DataGridTextColumn Header="客户名称" Binding="{Binding CustomerName}" Width="120"/>
                <DataGridTextColumn Header="销售人" Binding="{Binding Salesperson}" Width="100"/>
                <DataGridTextColumn Header="备注" Binding="{Binding Remarks}" Width="*"/>
            </DataGrid.Columns>
            
            <!-- 双击选择 -->
            <DataGrid.InputBindings>
                <MouseBinding MouseAction="LeftDoubleClick" Command="{Binding ConfirmCommand}"/>
            </DataGrid.InputBindings>
        </DataGrid>

        <!-- 分页控件 -->
        <Grid Grid.Row="3" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 分页信息 -->
            <TextBlock Grid.Column="0" Text="{Binding PageInfo}" 
                       VerticalAlignment="Center" Foreground="#666" FontSize="12"/>

            <!-- 分页按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="首页" Width="60" Height="32" Margin="2,0"
                        Command="{Binding FirstPageCommand}" 
                        IsEnabled="{Binding CanGoPreviousPage}"/>
                
                <Button Content="上一页" Width="80" Height="32" Margin="2,0"
                        Command="{Binding PreviousPageCommand}" 
                        IsEnabled="{Binding CanGoPreviousPage}"/>
                
                <TextBlock Text="{Binding CurrentPage}" VerticalAlignment="Center" 
                           Margin="10,0" FontWeight="Bold"/>
                
                <Button Content="下一页" Width="80" Height="32" Margin="2,0"
                        Command="{Binding NextPageCommand}" 
                        IsEnabled="{Binding CanGoNextPage}"/>
                
                <Button Content="尾页" Width="60" Height="32" Margin="2,0"
                        Command="{Binding LastPageCommand}" 
                        IsEnabled="{Binding CanGoNextPage}"/>
            </StackPanel>
        </Grid>

        <!-- 操作按钮 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button Content="确定" Width="80" Height="35" Margin="0,0,10,0"
                    Command="{Binding ConfirmCommand}" IsDefault="True"/>
            <Button Content="取消" Width="80" Height="35" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>



