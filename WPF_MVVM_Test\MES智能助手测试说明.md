# MES 智能助手测试说明

## 测试步骤

### 1. 启动应用程序

- 运行 WPF_MVVM_Test 应用程序
- 等待应用程序完全加载

### 2. 进入 MES 智能助手页面

- 在左侧菜单中点击"🏭 MES 智能助手"
- 观察页面是否正确加载

### 3. 检查服务状态

- 查看页面顶部的服务状态栏
- 应该显示以下状态之一：
  - "服务正常" (绿色) - 表示服务可用
  - "服务不可用 - 请检查网络连接" (红色) - 表示服务无法连接
  - "正在检查服务状态... (尝试 X/3)" - 表示正在检查中
  - "服务检查失败，X 秒后重试..." - 表示重试中
- 注意：系统会自动重试 3 次，每次间隔 3 秒
- 可以点击"🔄 重新检查"按钮手动重新检查服务状态

### 4. 测试消息发送（当服务可用时）

- 在输入框中输入测试消息，例如：
  - "你好，请介绍一下 MES 系统"
  - "MES 系统有哪些主要功能？"
  - "如何优化生产流程？"
- 点击"发送"按钮或按 Enter 键
- 观察是否收到 AI 回复

### 5. 测试界面交互

- 测试 Enter 键发送消息
- 测试 Shift+Enter 换行
- 测试清空聊天记录功能
- 测试滚动到底部功能
- 测试"🔄 重新检查"按钮功能

## 预期结果

### 服务可用时

1. 服务状态显示为"服务正常"（绿色）
2. 输入框可以正常输入
3. 发送按钮可以点击
4. 能够收到 AI 的智能回复
5. 聊天记录正常显示

### 服务不可用时

1. 服务状态显示为"服务不可用"（红色）
2. 输入框被禁用
3. 发送按钮不可点击
4. 显示服务不可用的提示消息

## 故障排除

### 如果服务状态显示"服务不可用"

1. 点击"🔄 重新检查"按钮手动重试
2. 检查网络连接
3. 确认服务地址 `http://101.200.57.145:8888` 是否可访问
4. 检查防火墙设置
5. 等待更长时间（服务响应可能较慢，超时时间已设置为 5 分钟）
6. 联系系统管理员确认服务状态

### 如果发送消息没有回复

1. 检查网络连接稳定性
2. 等待更长时间（服务可能需要处理时间，最长 5 分钟）
3. 尝试发送更简单的消息
4. 查看应用程序日志（Debug 输出）
5. 点击"🔄 重新检查"按钮确认服务状态

## 测试用例

### 基本功能测试

- [ ] 页面正常加载
- [ ] 服务状态检查正常（自动重试 3 次）
- [ ] 欢迎消息显示
- [ ] 输入框正常工作
- [ ] 发送按钮正常工作
- [ ] 重新检查按钮正常工作

### 消息发送测试

- [ ] 发送简单问题
- [ ] 发送复杂问题
- [ ] 发送中文消息
- [ ] 发送英文消息
- [ ] 发送特殊字符

### 界面交互测试

- [ ] Enter 键发送
- [ ] Shift+Enter 换行
- [ ] 滚动功能
- [ ] 消息时间戳显示
- [ ] 用户/AI 消息区分
- [ ] 重新检查按钮功能

### 错误处理测试

- [ ] 网络断开时的处理
- [ ] 服务超时的处理
- [ ] 空消息的处理
- [ ] 长消息的处理

## 注意事项

1. 测试时请确保网络连接稳定
2. 如果服务响应较慢，请耐心等待（超时时间为 5 分钟）
3. 系统会自动重试 3 次健康检查，每次间隔 3 秒
4. 可以使用"🔄 重新检查"按钮手动重新检查服务状态
5. 测试过程中如发现问题，请记录详细的错误信息
6. 建议在不同网络环境下进行测试
7. 可以同时测试原有 AI 助手功能，确保两者互不影响
