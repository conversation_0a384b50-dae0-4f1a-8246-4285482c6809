<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.HomePage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border Background="White"
            CornerRadius="5"
            Padding="20"
            Margin="0,0,0,20">
        <StackPanel>
            <TextBlock Text="🎉 欢迎使用管理系统"
                       FontSize="24"
                       FontWeight="Bold"
                       Margin="0,0,0,10"/>
            <TextBlock Text="这是系统首页，您可以在这里查看系统概况和重要信息。"
                       FontSize="14"
                       Margin="0,0,0,20"/>

            <UniformGrid Columns="3"
                         Margin="0,10,0,0">
                <Border Background="#4CAF50"
                        CornerRadius="5"
                        Margin="0,0,10,0"
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="📊 数据统计"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                        <TextBlock Text="总用户: 1,234"
                                   FontSize="14"
                                   Foreground="White"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <Border Background="#2196F3"
                        CornerRadius="5"
                        Margin="5,0"
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="🔔 系统通知"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                        <TextBlock Text="3条新消息"
                                   FontSize="14"
                                   Foreground="White"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

                <Border Background="#FF9800"
                        CornerRadius="5"
                        Margin="10,0,0,0"
                        Padding="15">
                    <StackPanel>
                        <TextBlock Text="⚡ 系统状态"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   Foreground="White"/>
                        <TextBlock Text="运行正常"
                                   FontSize="14"
                                   Foreground="White"
                                   Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>
            </UniformGrid>

            <!-- 快速操作区域 -->
            <Border Background="#F8F9FA"
                    CornerRadius="5"
                    Padding="20"
                    Margin="0,20,0,0">
                <StackPanel>
                    <TextBlock Text="🚀 快速操作"
                               FontSize="18"
                               FontWeight="Bold"
                               Margin="0,0,0,15"/>
                    
                    <UniformGrid Columns="4">
                        <Button Content="👥 用户管理"
                                Background="#E3F2FD"
                                Foreground="#1976D2"
                                BorderThickness="0"
                                Padding="15,10"
                                Margin="5"
                                Cursor="Hand"/>
                        <Button Content="📊 报表管理"
                                Background="#E8F5E8"
                                Foreground="#388E3C"
                                BorderThickness="0"
                                Padding="15,10"
                                Margin="5"
                                Cursor="Hand"/>
                        <Button Content="⚙️ 系统设置"
                                Background="#FFF3E0"
                                Foreground="#F57C00"
                                BorderThickness="0"
                                Padding="15,10"
                                Margin="5"
                                Cursor="Hand"/>
                        <Button Content="📈 数据分析"
                                Background="#F3E5F5"
                                Foreground="#7B1FA2"
                                BorderThickness="0"
                                Padding="15,10"
                                Margin="5"
                                Cursor="Hand"/>
                    </UniformGrid>
                </StackPanel>
            </Border>
        </StackPanel>
    </Border>
</UserControl>
