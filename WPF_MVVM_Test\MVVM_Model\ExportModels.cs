using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model
{
    /// <summary>
    /// 鱼塘数据模型
    /// </summary>
    public class FishPond
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public int TypeId { get; set; }
        public string TypeName { get; set; } = string.Empty;
        public int Status { get; set; }
        public string StatusText { get; set; } = string.Empty;
        public double Area { get; set; }
        public int Capacity { get; set; }
        public DateTime CreateTime { get; set; }
        public DateTime? UpdateTime { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// 鱼塘类型模型
    /// </summary>
    public class FishPondType
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreateTime { get; set; }
    }

    /// <summary>
    /// 鱼塘统计数据模型
    /// </summary>
    public class FishPondStatistics
    {
        public string PondName { get; set; } = string.Empty;
        public string TypeName { get; set; } = string.Empty;
        public int TotalFish { get; set; }
        public double TotalWeight { get; set; }
        public double AverageWeight { get; set; }
        public int FeedingCount { get; set; }
        public double WaterTemperature { get; set; }
        public double OxygenLevel { get; set; }
        public DateTime StatDate { get; set; }
    }

    /// <summary>
    /// 导出格式信息模型（从API返回的详细格式信息）
    /// </summary>
    public class ExportFormatInfo
    {
        [JsonPropertyName("format")]
        public string Format { get; set; } = string.Empty;

        [JsonPropertyName("description")]
        public string Description { get; set; } = string.Empty;

        [JsonPropertyName("mimeType")]
        public string MimeType { get; set; } = string.Empty;

        public override string ToString() => Format;
    }

    /// <summary>
    /// 导出任务模型
    /// </summary>
    public class ExportTask
    {
        [JsonPropertyName("TaskId")]
        public string TaskId { get; set; } = string.Empty;
        
        [JsonPropertyName("ExportType")]
        public string ExportType { get; set; } = string.Empty;
        
        [JsonPropertyName("Status")]
        public string Status { get; set; } = string.Empty;
        
        [JsonPropertyName("SubmittedAt")]
        public DateTime SubmittedAt { get; set; }
        
        [JsonPropertyName("Message")]
        public string Message { get; set; } = string.Empty;
        
        [JsonPropertyName("Progress")]
        public int Progress { get; set; }
        
        [JsonPropertyName("CreatedAt")]
        public DateTime CreatedAt { get; set; }
        
        [JsonPropertyName("CompletedAt")]
        public DateTime? CompletedAt { get; set; }
        
        [JsonPropertyName("FilePath")]
        public string FilePath { get; set; } = string.Empty;
        
        [JsonPropertyName("FileName")]
        public string FileName { get; set; } = string.Empty;
        
        [JsonPropertyName("FileSize")]
        public string FileSize { get; set; } = "0"; // 改为string类型以处理不同的数据格式
        
        [JsonPropertyName("ProcessingTime")]
        public string ProcessingTime { get; set; } = string.Empty;
        
        [JsonPropertyName("Priority")]
        public int Priority { get; set; }
        
        [JsonPropertyName("Format")]
        public string Format { get; set; } = "xlsx";
        
        [JsonPropertyName("Parameters")]
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
    }

    /// <summary>
    /// 导出请求模型
    /// </summary>
    public class ExportRequest
    {
        public string ExportType { get; set; } = string.Empty;
        public Dictionary<string, object> Parameters { get; set; } = new Dictionary<string, object>();
        public int Priority { get; set; } = 5;
        public string Format { get; set; } = "xlsx";
        public bool EmailNotification { get; set; } = false;
        public string EmailAddress { get; set; } = string.Empty;
    }

    /// <summary>
    /// 导出统计信息模型
    /// </summary>
    public class ExportStatistics
    {
        public int TotalExports { get; set; }
        public int SuccessExports { get; set; }
        public int FailedExports { get; set; }
        public long TotalSize { get; set; }
        
        public string TotalSizeText => $"{TotalSize / 1024.0:F1} KB";
    }

    /// <summary>
    /// MQ消息模型
    /// </summary>
    public class ExportNotification
    {
        public string TaskId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public bool AutoDownload { get; set; }
        public string DownloadUrl { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 导出格式枚举
    /// </summary>
    public enum ExportFormat
    {
        Excel,
        Csv,
        Pdf
    }

    /// <summary>
    /// 导出类型枚举
    /// </summary>
    public enum ExportType
    {
        FishData,
        FishType,
        Statistics
    }

    /// <summary>
    /// 任务状态枚举
    /// </summary>
    public enum TaskStatus
    {
        Pending,
        Processing,
        Completed,
        Failed,
        Cancelled
    }

    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Info,
        Success,
        Warning,
        Error
    }

    /// <summary>
    /// 操作日志模型
    /// </summary>
    public class OperationLog
    {
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string Message { get; set; } = string.Empty;
        public LogLevel Level { get; set; } = LogLevel.Info;
        
        public string TimeText => Timestamp.ToString("HH:mm:ss");
        public string LevelText => Level switch
        {
            LogLevel.Success => "✅",
            LogLevel.Error => "❌",
            LogLevel.Warning => "⚠️",
            _ => "📋"
        };
    }

    /// <summary>
    /// API响应通用模型 - 匹配实际服务器返回格式
    /// </summary>
    public class ApiResponse<T>
    {
        [JsonPropertyName("Result")]
        public object? Result { get; set; }

        [JsonPropertyName("Data")]
        public T? Data { get; set; }

        [JsonPropertyName("Success")]
        public bool Success { get; set; }

        [JsonPropertyName("Message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("Code")]
        public int Code { get; set; }

        [JsonPropertyName("Timestamp")]
        public DateTime? Timestamp { get; set; }
    }

    /// <summary>
    /// API响应基类（无泛型）
    /// </summary>
    public class ApiResponse
    {
        [JsonPropertyName("Success")]
        public bool Success { get; set; }

        [JsonPropertyName("Message")]
        public string Message { get; set; } = string.Empty;

        [JsonPropertyName("Code")]
        public int Code { get; set; }

        [JsonPropertyName("Timestamp")]
        public DateTime? Timestamp { get; set; }
    }
}