<Window x:Class="WPF_MVVM_Test.MVVM_View.Bom.ProcessRouteSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="选择工艺路线"
        Height="600"
        Width="1000"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 状态颜色转换器 -->
        <Style x:Key="StatusColorConverter" TargetType="Border">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="停用">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="选择工艺路线"
                   FontSize="20"
                   FontWeight="Bold"
                   Margin="0,0,0,20"/>

        <!-- 搜索区域 -->
        <materialDesign:Card Grid.Row="1" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBox Grid.Column="0"
                         Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="请输入工艺路线名称或编号"
                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                         Height="40"/>

                <Button Grid.Column="2"
                        Content="搜索"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding SearchCommand}"
                        Width="80"
                        Height="40"
                        Margin="10,0,0,0"/>
            </Grid>
        </materialDesign:Card>

        <!-- 数据表格 -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 加载指示器 -->
                <ProgressBar Grid.Row="0"
                             Style="{StaticResource MaterialDesignCircularProgressBar}"
                             Value="0"
                             IsIndeterminate="True"
                             Width="50"
                             Height="50"
                             HorizontalAlignment="Center"
                             VerticalAlignment="Center"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- 数据表格 -->
                <DataGrid Grid.Row="0"
                          ItemsSource="{Binding ProcessRoutes}"
                          SelectedItem="{Binding SelectedProcessRoute}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserResizeRows="False"
                          CanUserSortColumns="True"
                          HeadersVisibility="Column"
                          GridLinesVisibility="All"
                          AlternatingRowBackground="#F8F8F8"
                          RowBackground="White"
                          BorderBrush="#E0E0E0"
                          BorderThickness="1"
                          FontSize="12"
                          SelectionMode="Single">

                    <DataGrid.Style>
                        <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                            <Setter Property="Visibility" Value="Visible"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.Style>

                    <DataGrid.Columns>
                        <!-- 选择列 -->
                        <DataGridTemplateColumn Header="选择" Width="60">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <RadioButton GroupName="ProcessRouteSelection"
                                                 IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True">
                            <DataGridTextColumn.Binding>
                                <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}" 
                                         Path="Header"/>
                            </DataGridTextColumn.Binding>
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 工艺路线编号 -->
                        <DataGridTextColumn Header="工艺路线编号" 
                                            Binding="{Binding ProcessRouteNumber}" 
                                            Width="150"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 工艺路线名称 -->
                        <DataGridTextColumn Header="工艺路线名称" 
                                            Binding="{Binding ProcessRouteName}" 
                                            Width="200"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 状态 -->
                        <DataGridTemplateColumn Header="状态" Width="80">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10"
                                            Padding="8,2"
                                            HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Setter Property="Background" Value="#4CAF50"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="停用">
                                                        <Setter Property="Background" Value="#F44336"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}"
                                                   Foreground="White"
                                                   FontSize="10"
                                                   FontWeight="Bold"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 工艺步骤数 -->
                        <DataGridTextColumn Header="工艺步骤数" 
                                            Binding="{Binding ProcessStepCount}" 
                                            Width="100"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 描述 -->
                        <DataGridTextColumn Header="工艺路线说明" 
                                            Binding="{Binding Description}" 
                                            Width="*"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Margin" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 分页控件 -->
                <Grid Grid.Row="1" Background="#F5F5F5" Height="50">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="{Binding PageInfo}"
                               VerticalAlignment="Center"
                               Margin="15,0,0,0"
                               FontSize="12"
                               Foreground="#666"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,15,0">
                        <Button Content="首页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding FirstPageCommand}"
                                Width="60"
                                Height="30"
                                Margin="0,0,5,0"/>
                        
                        <Button Content="上一页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding PreviousPageCommand}"
                                Width="60"
                                Height="30"
                                Margin="0,0,5,0"/>
                        
                        <Button Content="下一页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding NextPageCommand}"
                                Width="60"
                                Height="30"
                                Margin="0,0,5,0"/>
                        
                        <Button Content="末页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding LastPageCommand}"
                                Width="60"
                                Height="30"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="3" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,15,0,0">
            <Button Content="取消"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="80"
                    Height="35"
                    Margin="0,0,10,0"
                    Click="CancelButton_Click"/>
            
            <Button Content="确定"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="80"
                    Height="35"
                    Click="ConfirmButton_Click"/>
        </StackPanel>
    </Grid>
</Window>