using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.WorkOrderTask;

namespace WPF_MVVM_Test.MVVM_ViewModel.WorkOrderTask
{
    /// <summary>
    /// 报工对话框ViewModel
    /// </summary>
    public class WorkReportDialogViewModel : BaseViewModel
    {
        #region 私有字段
        private WorkOrderTaskModel _currentTask;
        private string _inspectionName;
        private string _inspectionType;
        private string _inspectionDepartment;
        private int _reportQuantity;
        private int _testQuantity;
        private int _qualifiedQuantity;
        private int _unqualifiedQuantity;
        private string _overallResult;
        private string _remark;
        private bool _dialogResult;
        #endregion

        #region 公共属性
        /// <summary>
        /// 当前任务
        /// </summary>
        public WorkOrderTaskModel CurrentTask
        {
            get => _currentTask;
            set => SetProperty(ref _currentTask, value);
        }

        /// <summary>
        /// 检验项名称
        /// </summary>
        public string InspectionName
        {
            get => _inspectionName;
            set => SetProperty(ref _inspectionName, value);
        }

        /// <summary>
        /// 检验类型
        /// </summary>
        public string InspectionType
        {
            get => _inspectionType;
            set => SetProperty(ref _inspectionType, value);
        }

        /// <summary>
        /// 检验部门
        /// </summary>
        public string InspectionDepartment
        {
            get => _inspectionDepartment;
            set => SetProperty(ref _inspectionDepartment, value);
        }

        /// <summary>
        /// 报告数量
        /// </summary>
        public int ReportQuantity
        {
            get => _reportQuantity;
            set
            {
                SetProperty(ref _reportQuantity, value);
                UpdateCalculations();
            }
        }

        /// <summary>
        /// 测试数量
        /// </summary>
        public int TestQuantity
        {
            get => _testQuantity;
            set
            {
                SetProperty(ref _testQuantity, value);
                UpdateCalculations();
            }
        }

        /// <summary>
        /// 合格数量
        /// </summary>
        public int QualifiedQuantity
        {
            get => _qualifiedQuantity;
            set
            {
                SetProperty(ref _qualifiedQuantity, value);
                UpdateCalculations();
            }
        }

        /// <summary>
        /// 不合格数量
        /// </summary>
        public int UnqualifiedQuantity
        {
            get => _unqualifiedQuantity;
            set
            {
                SetProperty(ref _unqualifiedQuantity, value);
                UpdateCalculations();
            }
        }

        /// <summary>
        /// 总体结果
        /// </summary>
        public string OverallResult
        {
            get => _overallResult;
            set => SetProperty(ref _overallResult, value);
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark
        {
            get => _remark;
            set => SetProperty(ref _remark, value);
        }

        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool DialogResult
        {
            get => _dialogResult;
            set => SetProperty(ref _dialogResult, value);
        }
        #endregion

        #region 命令
        public ICommand ConfirmCommand { get; private set; }
        public ICommand CancelCommand { get; private set; }
        #endregion

        #region 构造函数
        public WorkReportDialogViewModel(WorkOrderTaskModel task)
        {
            CurrentTask = task;
            InitializeData();
            InitializeCommands();
        }
        #endregion

        #region 私有方法
        private void InitializeData()
        {
            if (CurrentTask != null)
            {
                InspectionName = CurrentTask.TaskName;
                InspectionType = "质量检验";
                InspectionDepartment = "质量部";
                ReportQuantity = 10;
                TestQuantity = 10;
                QualifiedQuantity = 10;
                UnqualifiedQuantity = 0;
                OverallResult = "合格";
                Remark = "";
            }
        }

        private void InitializeCommands()
        {
            ConfirmCommand = new RelayCommand(Confirm, CanConfirm);
            CancelCommand = new RelayCommand(Cancel);
        }

        private void UpdateCalculations()
        {
            // 自动计算总体结果
            if (QualifiedQuantity + UnqualifiedQuantity == TestQuantity && UnqualifiedQuantity == 0)
            {
                OverallResult = "合格";
            }
            else if (UnqualifiedQuantity > 0)
            {
                OverallResult = "不合格";
            }
            else
            {
                OverallResult = "待定";
            }
        }

        private bool CanConfirm()
        {
            return !string.IsNullOrEmpty(InspectionName) &&
                   ReportQuantity > 0 &&
                   TestQuantity > 0 &&
                   (QualifiedQuantity + UnqualifiedQuantity) <= TestQuantity;
        }

        private void Confirm()
        {
            DialogResult = true;
        }

        private void Cancel()
        {
            DialogResult = false;
        }

        /// <summary>
        /// 创建报工请求数据
        /// </summary>
        /// <returns></returns>
        public WorkReportRequest CreateWorkReportRequest()
        {
            return new WorkReportRequest
            {
                WorkOrderTaskId = CurrentTask.Id,
                InspectionName = InspectionName,
                InspectionType = InspectionType,
                ProductId = "默认产品ID", // 需要从任务中获取
                ProcessStepId = "默认工艺步骤ID", // 需要从任务中获取
                StationId = CurrentTask.StationName,
                ReportId = Guid.NewGuid().ToString(),
                InspectorId = "当前用户ID", // 需要从当前登录用户获取
                ReportQuantity = ReportQuantity,
                ReportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                InspectionTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                InspectionDepartment = InspectionDepartment,
                TestQuantity = TestQuantity,
                QualifiedQuantity = QualifiedQuantity,
                UnqualifiedQuantity = UnqualifiedQuantity,
                OverallResult = OverallResult,
                Remark = Remark
            };
        }
        #endregion
    }
}
