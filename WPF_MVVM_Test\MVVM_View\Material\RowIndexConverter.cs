using System;
using System.Globalization;
using System.Windows.Controls;
using System.Windows.Data;

namespace WPF_MVVM_Test.MVVM_View.Material
{
    /// <summary>
    /// 行索引转换器
    /// </summary>
    public class RowIndexConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DataGridRow row)
            {
                var dataGrid = ItemsControl.ItemsControlFromItemContainer(row) as DataGrid;
                if (dataGrid != null)
                {
                    int index = dataGrid.ItemContainerGenerator.IndexFromContainer(row);
                    return (index + 1).ToString();
                }
            }
            return "0";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}