using System.Windows;
using WPF_MVVM_Test.MVVM_Model.WorkOrderTask;
using WPF_MVVM_Test.MVVM_ViewModel.WorkOrderTask;

namespace WPF_MVVM_Test.MVVM_View.WorkOrderTask
{
    /// <summary>
    /// WorkOrderTaskEditDialog.xaml 的交互逻辑
    /// </summary>
    public partial class WorkOrderTaskEditDialog : Window
    {
        private WorkOrderTaskEditViewModel _viewModel;

        public bool IsConfirmed { get; private set; }

        public WorkOrderTaskEditDialog(WorkOrderTaskModel task, bool isEditMode = false)
        {
            InitializeComponent();
            
            _viewModel = new WorkOrderTaskEditViewModel(task, isEditMode);
            DataContext = _viewModel;

            // 订阅ViewModel的关闭事件
            _viewModel.RequestClose += OnRequestClose;
        }

        private void OnRequestClose(bool result)
        {
            IsConfirmed = result;
            DialogResult = result;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // 取消订阅事件
            if (_viewModel != null)
            {
                _viewModel.RequestClose -= OnRequestClose;
            }
            base.OnClosed(e);
        }
    }
}
