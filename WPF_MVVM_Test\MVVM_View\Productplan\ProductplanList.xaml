﻿<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Productplan.ProductplanList"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel"
             mc:Ignorable="d">
    
    <!-- 添加资源 -->
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>
    
    <UserControl.InputBindings>
        <!-- Enter键触发搜索 -->
        <KeyBinding Key="Enter" Command="{Binding SearchCommand}"/>
        <!-- F5键刷新 -->
        <KeyBinding Key="F5" Command="{Binding RefreshCommand}"/>
        <!-- Ctrl+L清空搜索 -->
        <KeyBinding Key="L" Modifiers="Ctrl" Command="{Binding ClearSearchCommand}"/>
    </UserControl.InputBindings>
    
    <Grid Background="#F5F5F5">
        <!-- 顶部工具栏 - 更新为包含搜索框 -->
        <Border Height="120" VerticalAlignment="Top" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid Margin="20,10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 第一行：搜索条件 -->
                <Grid Grid.Row="0" Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="200"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="150"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 计划编号/计划名称搜索 -->
                    <TextBlock Grid.Column="0" Text="计划编号/名称:" VerticalAlignment="Center" 
                               Margin="0,0,10,0" FontSize="12"/>
                    <TextBox Grid.Column="1" Height="35" Padding="10,8" 
                             Text="{Binding SearchPlanNumber, UpdateSourceTrigger=PropertyChanged}" 
                             VerticalAlignment="Center" Margin="0,0,20,0"
                             ToolTip="输入计划编号或计划名称进行搜索"/>

                    <!-- 单位搜索 -->
                    <TextBlock Grid.Column="2" Text="单位:" VerticalAlignment="Center" 
                               Margin="0,0,10,0" FontSize="12"/>
                    <TextBox Grid.Column="3" Height="35" Padding="10,8" 
                             Text="{Binding SearchUnit, UpdateSourceTrigger=PropertyChanged}" 
                             VerticalAlignment="Center" Margin="0,0,20,0"
                             ToolTip="输入单位进行搜索"/>

                    <!-- 状态搜索 -->
                    <TextBlock Grid.Column="4" Text="状态:" VerticalAlignment="Center" 
                               Margin="0,0,10,0" FontSize="12"/>
                    <ComboBox Grid.Column="5" Height="35" 
                              ItemsSource="{Binding StatusOptions}"
                              SelectedValue="{Binding SearchStatus}"
                              SelectedValuePath="Value"
                              DisplayMemberPath="Text"
                              VerticalAlignment="Center" Margin="0,0,20,0"
                              ToolTip="选择状态进行筛选"/>
                </Grid>

                <!-- 第二行：操作按钮 -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Left">
                    <Button Content="🔍 搜索" Background="#1890FF" Foreground="White" 
                            BorderThickness="0" Padding="20,8" Margin="0,0,10,0" Cursor="Hand"
                            Command="{Binding SearchCommand}" FontSize="12"/>
                    <Button Content="🔄 刷新" Background="#52C41A" Foreground="White" 
                            BorderThickness="0" Padding="20,8" Margin="0,0,10,0" Cursor="Hand"
                            Command="{Binding RefreshCommand}" FontSize="12"/>
                    <Button Content="🗑️ 清空" Background="#FF7875" Foreground="White" 
                            BorderThickness="0" Padding="20,8" Margin="0,0,10,0" Cursor="Hand"
                            Command="{Binding ClearSearchCommand}" FontSize="12"/>
                    
                    <!-- 加载状态指示器 - 修复版 -->
                    <Border Background="#F0F0F0" CornerRadius="3" Padding="10,8" Margin="20,0,0,0"
                            Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <!-- 简化的加载图标 -->
                            <TextBlock Text="⏳" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                            <TextBlock Text="加载中..." VerticalAlignment="Center" FontSize="12" Foreground="#666"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 自动刷新状态指示器 -->
                    <Border Background="#E6F7FF" CornerRadius="3" Padding="10,8" Margin="10,0,0,0"
                            Visibility="{Binding IsAutoRefreshEnabled, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🔄" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                            <TextBlock Text="{Binding AutoRefreshStatusText}" VerticalAlignment="Center" 
                                       FontSize="11" Foreground="#1890FF"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 最后更新时间显示 -->
                    <Border Background="#F6FFED" CornerRadius="3" Padding="10,8" Margin="10,0,0,0">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="🕒" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                            <TextBlock Text="{Binding LastRefreshTimeText}" VerticalAlignment="Center" 
                                       FontSize="11" Foreground="#52C41A"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 主内容区域 - 调整上边距 -->
        <Border Background="White" CornerRadius="5" Padding="20" 
                VerticalAlignment="Stretch" Margin="0,130,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 页面标题和操作按钮 -->
                <Grid Grid.Row="0" Margin="0,0,0,20">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧标题和消息 -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal">
                        <TextBlock Text="📋 生产计划" FontSize="20" FontWeight="Bold" VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding Message}" FontSize="12" Foreground="#666"
                                   VerticalAlignment="Center" Margin="20,0,0,0"/>
                    </StackPanel>

                    <!-- 右侧新增按钮 -->
                    <Button Grid.Column="1" Content="➕ 新增计划"
                            Background="#52C41A" Foreground="White"
                            BorderThickness="0" Padding="15,8" Cursor="Hand"
                            Command="{Binding AddProductPlanCommand}"
                            FontSize="14" FontWeight="Bold"
                            ToolTip="添加新的生产计划"/>
                </Grid>

                <!-- 生产计划列表 -->
                <DataGrid Grid.Row="1" ItemsSource="{Binding ProductPlans}"
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" CanUserDeleteRows="False" IsReadOnly="True"
                          GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                          Background="White" RowBackground="White" AlternatingRowBackground="#FAFAFA"
                          BorderThickness="1" BorderBrush="#E0E0E0"
                          FontSize="12">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="70"/>
                        <DataGridTextColumn Header="计划编号" Binding="{Binding PlanNumber}" Width="120"/>
                        <DataGridTextColumn Header="计划名称" Binding="{Binding PlanName}" Width="120"/>
                        <DataGridTextColumn Header="产品名称" Binding="{Binding ProductName}" Width="120"/>
                        <DataGridTextColumn Header="产品编号" Binding="{Binding ProductNumber}" Width="120"/>
                        <DataGridTextColumn Header="规格型号" Binding="{Binding Specification}" Width="120"/>
                        <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="70"/>
                        <!-- 计划数量列 - 显示decimal格式，保留2位小数 -->
                        <DataGridTextColumn Header="计划数量" Width="80">
                            <DataGridTextColumn.Binding>
                                <Binding Path="PlanQuantity" StringFormat="{}{0:F2}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="来源类型" Binding="{Binding SourceType}" Width="120"/>
                        <DataGridTextColumn Header="计划开工" Binding="{Binding PlanStartTime, StringFormat=yyyy-MM-dd}" Width="90"/>
                        <DataGridTextColumn Header="计划完工" Binding="{Binding PlanEndTime, StringFormat=yyyy-MM-dd}" Width="90"/>
                        
                        <!-- 状态列 -->
                        <DataGridTemplateColumn Header="状态" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Background="{Binding StatusColor}" CornerRadius="3" Padding="5,2">
                                        <TextBlock Text="{Binding StatusText}" Foreground="White" 
                                                 HorizontalAlignment="Center" FontSize="11"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="编辑" Background="#1890FF" Foreground="White" 
                                                BorderThickness="0" Padding="6,3" Margin="1" Cursor="Hand" FontSize="10"
                                                Command="{Binding DataContext.EditCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"/>
                                        <Button Content="分解" Background="#FA8C16" Foreground="White" 
                                                BorderThickness="0" Padding="6,3" Margin="1" Cursor="Hand" FontSize="10"
                                                Command="{Binding DataContext.DecomposeCommand, RelativeSource={RelativeSource AncestorType=DataGrid}}"
                                                CommandParameter="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 分页控件区域 -->
                <Grid Grid.Row="2" Margin="0,20,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 左侧分页信息 -->
                    <TextBlock Grid.Column="0" Text="{Binding PageInfo}" 
                               VerticalAlignment="Center" Foreground="#666" FontSize="12"/>

                    <!-- 中间分页按钮 -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                        <!-- 首页按钮 -->
                        <Button Content="首页" Width="60" Height="32" Margin="2,0"
                                Background="#F0F0F0" Foreground="#333" BorderBrush="#D9D9D9" BorderThickness="1"
                                Command="{Binding FirstPageCommand}" 
                                IsEnabled="{Binding CanGoPreviousPage}"
                                Cursor="Hand" FontSize="12"/>
                        
                        <!-- 上一页按钮 -->
                        <Button Content="上一页" Width="80" Height="32" Margin="2,0"
                                Background="#F0F0F0" Foreground="#333" BorderBrush="#D9D9D9" BorderThickness="1"
                                Command="{Binding PreviousPageCommand}" 
                                IsEnabled="{Binding CanGoPreviousPage}"
                                Cursor="Hand" FontSize="12"/>

                        <!-- 页码显示和输入 -->
                        <Border Background="White" BorderBrush="#D9D9D9" BorderThickness="1" 
                                Height="32" Margin="10,0" Padding="8,0">
                            <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                <TextBlock Text="第" VerticalAlignment="Center" FontSize="12"/>
                                <TextBox Text="{Binding CurrentPage, UpdateSourceTrigger=PropertyChanged}" 
                                         Width="40" Height="20" Margin="5,0" TextAlignment="Center"
                                         BorderThickness="0" FontSize="12" VerticalAlignment="Center"/>
                                <TextBlock Text="页" VerticalAlignment="Center" FontSize="12" Margin="0,0,5,0"/>
                                <TextBlock Text="/" VerticalAlignment="Center" FontSize="12" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding TotalPages}" VerticalAlignment="Center" FontSize="12"/>
                                <TextBlock Text="页" VerticalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </Border>

                        <!-- 下一页按钮 -->
                        <Button Content="下一页" Width="80" Height="32" Margin="2,0"
                                Background="#F0F0F0" Foreground="#333" BorderBrush="#D9D9D9" BorderThickness="1"
                                Command="{Binding NextPageCommand}" 
                                IsEnabled="{Binding CanGoNextPage}"
                                Cursor="Hand" FontSize="12"/>
                        
                        <!-- 尾页按钮 -->
                        <Button Content="尾页" Width="60" Height="32" Margin="2,0"
                                Background="#F0F0F0" Foreground="#333" BorderBrush="#D9D9D9" BorderThickness="1"
                                Command="{Binding LastPageCommand}" 
                                IsEnabled="{Binding CanGoNextPage}"
                                Cursor="Hand" FontSize="12"/>
                    </StackPanel>

                </Grid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
