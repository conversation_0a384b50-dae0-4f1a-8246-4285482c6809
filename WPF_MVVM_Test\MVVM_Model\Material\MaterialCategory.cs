using System;
using System.Collections.Generic;
using System.ComponentModel;

namespace WPF_MVVM_Test.MVVM_Model.Material
{
    /// <summary>
    /// 物料分类模型（树形结构）
    /// </summary>
    public class MaterialCategory : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _categoryName = string.Empty;
        private string _parentId = string.Empty;
        private int _level = 0;
        private bool _isExpanded = false;
        private bool _isSelected = false;
        private List<MaterialCategory> _children = new List<MaterialCategory>();

        /// <summary>
        /// 分类ID
        /// </summary>
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 分类名称
        /// </summary>
        public string CategoryName
        {
            get => _categoryName;
            set
            {
                if (_categoryName != value)
                {
                    _categoryName = value;
                    OnPropertyChanged(nameof(CategoryName));
                }
            }
        }

        /// <summary>
        /// API返回的名称字段（用于JSON反序列化）
        /// </summary>
        public string Name
        {
            get => _categoryName;
            set => CategoryName = value;
        }

        /// <summary>
        /// 父级分类ID
        /// </summary>
        public string ParentId
        {
            get => _parentId;
            set
            {
                if (_parentId != value)
                {
                    _parentId = value;
                    OnPropertyChanged(nameof(ParentId));
                }
            }
        }

        /// <summary>
        /// 层级
        /// </summary>
        public int Level
        {
            get => _level;
            set
            {
                if (_level != value)
                {
                    _level = value;
                    OnPropertyChanged(nameof(Level));
                }
            }
        }

        /// <summary>
        /// 是否展开
        /// </summary>
        public bool IsExpanded
        {
            get => _isExpanded;
            set
            {
                if (_isExpanded != value)
                {
                    _isExpanded = value;
                    OnPropertyChanged(nameof(IsExpanded));
                }
            }
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        /// <summary>
        /// 子分类列表
        /// </summary>
        public List<MaterialCategory> Children
        {
            get => _children;
            set
            {
                if (_children != value)
                {
                    _children = value;
                    OnPropertyChanged(nameof(Children));
                }
            }
        }

        /// <summary>
        /// 是否有子节点
        /// </summary>
        public bool HasChildren => Children != null && Children.Count > 0;

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 获取模拟数据（匹配API结构）
        /// </summary>
        /// <returns></returns>
        public static List<MaterialCategory> GetMockData()
        {
            return new List<MaterialCategory>
            {
                new MaterialCategory
                {
                    Id = "eebee2ec-8c56-42b6-9402-efc83949fc84",
                    CategoryName = "包装材料",
                    ParentId = "00000000-0000-0000-0000-000000000000",
                    Level = 0,
                    Children = new List<MaterialCategory>
                    {
                        new MaterialCategory
                        {
                            Id = "c7cac173-30c3-46ba-bd07-5378624f75a5",
                            CategoryName = "塑料袋",
                            ParentId = "eebee2ec-8c56-42b6-9402-efc83949fc84",
                            Level = 1,
                            Children = new List<MaterialCategory>()
                        },
                        new MaterialCategory
                        {
                            Id = "ca1ee54e-a323-42b7-a852-4246483914d9",
                            CategoryName = "纸箱",
                            ParentId = "eebee2ec-8c56-42b6-9402-efc83949fc84",
                            Level = 1,
                            Children = new List<MaterialCategory>()
                        }
                    }
                },
                new MaterialCategory
                {
                    Id = "762db92e-f6e5-4f75-904e-42a8e6f84d49",
                    CategoryName = "原材料",
                    ParentId = "00000000-0000-0000-0000-000000000000",
                    Level = 0,
                    Children = new List<MaterialCategory>
                    {
                        new MaterialCategory
                        {
                            Id = "a765376d-9c6b-4e14-a44c-5b5a04760e78",
                            CategoryName = "塑料材料",
                            ParentId = "762db92e-f6e5-4f75-904e-42a8e6f84d49",
                            Level = 1,
                            Children = new List<MaterialCategory>
                            {
                                new MaterialCategory
                                {
                                    Id = "ab485e94-5531-4e60-aa2f-c3d31b05f251",
                                    CategoryName = "PVC",
                                    ParentId = "a765376d-9c6b-4e14-a44c-5b5a04760e78",
                                    Level = 2,
                                    Children = new List<MaterialCategory>
                                    {
                                        new MaterialCategory
                                        {
                                            Id = "756feec6-bcef-4d30-9573-3e772a941dd3",
                                            CategoryName = "硬质 PVC",
                                            ParentId = "ab485e94-5531-4e60-aa2f-c3d31b05f251",
                                            Level = 3,
                                            Children = new List<MaterialCategory>
                                            {
                                                new MaterialCategory
                                                {
                                                    Id = "3d91bbf6-5a50-49e8-8bde-49908d92e804",
                                                    CategoryName = "汽车用硬 PVC",
                                                    ParentId = "756feec6-bcef-4d30-9573-3e772a941dd3",
                                                    Level = 4,
                                                    Children = new List<MaterialCategory>()
                                                },
                                                new MaterialCategory
                                                {
                                                    Id = "b928523c-5a6a-41ed-88fa-287dd3ca5ba0",
                                                    CategoryName = "电气用硬 PVC",
                                                    ParentId = "756feec6-bcef-4d30-9573-3e772a941dd3",
                                                    Level = 4,
                                                    Children = new List<MaterialCategory>()
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
    }
}