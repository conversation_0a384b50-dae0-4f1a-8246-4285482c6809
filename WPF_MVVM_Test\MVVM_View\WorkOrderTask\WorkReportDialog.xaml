<Window x:Class="WPF_MVVM_Test.MVVM_View.WorkOrderTask.WorkReportDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="工单报工" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="工单任务报工" FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- 报工表单 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- 基本信息 -->
                <GroupBox Header="基本信息" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="35"/>
                            <RowDefinition Height="35"/>
                            <RowDefinition Height="35"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="任务名称：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding CurrentTask.TaskName}" 
                                 IsReadOnly="True" Background="LightGray" VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="任务编号：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding CurrentTask.TaskNumber}" 
                                 IsReadOnly="True" Background="LightGray" VerticalAlignment="Center"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="检验项名称：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding InspectionName}" 
                                 VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="检验类型：" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="1" Grid.Column="3" SelectedValue="{Binding InspectionType}" 
                                  VerticalAlignment="Center">
                            <ComboBoxItem Content="质量检验"/>
                            <ComboBoxItem Content="首件检验"/>
                            <ComboBoxItem Content="过程检验"/>
                            <ComboBoxItem Content="最终检验"/>
                        </ComboBox>

                        <TextBlock Grid.Row="2" Grid.Column="0" Text="检验部门：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding InspectionDepartment}" 
                                 VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="2" Grid.Column="2" Text="站点：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="2" Grid.Column="3" Text="{Binding CurrentTask.StationName}" 
                                 IsReadOnly="True" Background="LightGray" VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>

                <!-- 数量信息 -->
                <GroupBox Header="数量信息" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="35"/>
                            <RowDefinition Height="35"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="报告数量：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding ReportQuantity}" 
                                 VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="0" Grid.Column="2" Text="测试数量：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="0" Grid.Column="3" Text="{Binding TestQuantity}" 
                                 VerticalAlignment="Center"/>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="合格数量：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding QualifiedQuantity}" 
                                 VerticalAlignment="Center" Margin="0,0,10,0"/>

                        <TextBlock Grid.Row="1" Grid.Column="2" Text="不合格数量：" VerticalAlignment="Center"/>
                        <TextBox Grid.Row="1" Grid.Column="3" Text="{Binding UnqualifiedQuantity}" 
                                 VerticalAlignment="Center"/>
                    </Grid>
                </GroupBox>

                <!-- 结果信息 -->
                <GroupBox Header="检验结果" Margin="0,0,0,15">
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="120"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="35"/>
                            <RowDefinition Height="80"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Grid.Column="0" Text="总体结果：" VerticalAlignment="Center"/>
                        <ComboBox Grid.Row="0" Grid.Column="1" SelectedValue="{Binding OverallResult}" 
                                  VerticalAlignment="Center">
                            <ComboBoxItem Content="合格"/>
                            <ComboBoxItem Content="不合格"/>
                            <ComboBoxItem Content="待定"/>
                        </ComboBox>

                        <TextBlock Grid.Row="1" Grid.Column="0" Text="备注：" VerticalAlignment="Top" Margin="0,5,0,0"/>
                        <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding Remark}" 
                                 TextWrapping="Wrap" AcceptsReturn="True" 
                                 VerticalScrollBarVisibility="Auto" Height="70"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="确定" Width="80" Height="30" Margin="0,0,10,0"
                    Command="{Binding ConfirmCommand}" IsDefault="True"/>
            <Button Content="取消" Width="80" Height="30"
                    Command="{Binding CancelCommand}" IsCancel="True"/>
        </StackPanel>
    </Grid>
</Window>
