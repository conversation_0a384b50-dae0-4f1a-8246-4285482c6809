using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.Material
{
    /// <summary>
    /// 物料API响应模型
    /// </summary>
    public class MaterialApiResponse
    {
        public MaterialData Data { get; set; } = new MaterialData();
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 物料数据模型
    /// </summary>
    public class MaterialData
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<Material> Data { get; set; } = new List<Material>();
    }
}