using System;
using System.Windows;
using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_ViewModel;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;

namespace WPF_MVVM_Test.MVVM_View.Productplan
{
    /// <summary>
    /// AddProductPlan.xaml 的交互逻辑
    /// </summary>
    public partial class AddProductPlan : System.Windows.Controls.UserControl
    {
        public AddProductPlan()
        {
            InitializeComponent();
            // 不在这里设置DataContext，让MainWindowViewModel来管理
        }

        /// <summary>
        /// 返回列表按钮点击事件
        /// </summary>
        private void BackToList_Click(object sender, RoutedEventArgs e)
        {
            // 通过查找父窗口的DataContext来切换页面
            var mainWindow = Window.GetWindow(this);
            if (mainWindow?.DataContext is MainWindowViewModel mainViewModel)
            {
                // 执行返回生产计划列表的命令
                if (mainViewModel.ShowProductPlanCommand.CanExecute(null))
                {
                    mainViewModel.ShowProductPlanCommand.Execute(null);
                }
            }
        }
    }
}
