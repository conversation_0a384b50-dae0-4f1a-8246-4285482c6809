<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.ReportManagement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border Background="White"
            CornerRadius="5"
            Padding="20"
            Margin="0,0,0,20">
        <StackPanel>
            <!-- 页面标题 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <TextBlock Text="📊 报表管理"
                           FontSize="20"
                           FontWeight="Bold"
                           VerticalAlignment="Center"/>
                <TextBlock Text="生成和管理系统报表"
                           FontSize="14"
                           Foreground="Gray"
                           VerticalAlignment="Center"
                           Margin="15,0,0,0"/>
            </StackPanel>

            <!-- 报表配置区域 -->
            <Border Background="#F8F9FA"
                    CornerRadius="5"
                    Padding="20"
                    Margin="0,0,0,20">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="120"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 报表类型 -->
                    <TextBlock Grid.Row="0" Grid.Column="0"
                               Text="报表类型："
                               VerticalAlignment="Center"
                               Margin="0,0,10,10"/>
                    <ComboBox Grid.Row="0" Grid.Column="1"
                              ItemsSource="{Binding ReportTypes}"
                              SelectedItem="{Binding SelectedReportType}"
                              Margin="0,0,20,10"
                              Padding="8"/>

                    <!-- 开始日期 -->
                    <TextBlock Grid.Row="0" Grid.Column="2"
                               Text="开始日期："
                               VerticalAlignment="Center"
                               Margin="0,0,10,10"/>
                    <DatePicker Grid.Row="0" Grid.Column="3"
                                Margin="0,0,0,10"
                                SelectedDate="{Binding StartDate}"
                                Padding="8"/>

                    <!-- 结束日期 -->
                    <TextBlock Grid.Row="1" Grid.Column="0"
                               Text="结束日期："
                               VerticalAlignment="Center"
                               Margin="0,0,10,10"/>
                    <DatePicker Grid.Row="1" Grid.Column="1"
                                Margin="0,0,20,10"
                                SelectedDate="{Binding EndDate}"
                                Padding="8"/>

                    <!-- 操作按钮 -->
                    <StackPanel Grid.Row="1" Grid.Column="2" Grid.ColumnSpan="2"
                                Orientation="Horizontal"
                                HorizontalAlignment="Right"
                                Margin="0,0,0,10">
                        <Button Content="🔄 生成报表"
                                Background="#4CAF50"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="15,8"
                                Margin="0,0,10,0"
                                Command="{Binding GenerateReportCommand}"
                                Cursor="Hand"/>
                        <Button Content="📤 导出报表"
                                Background="#2196F3"
                                Foreground="White"
                                BorderThickness="0"
                                Padding="15,8"
                                Command="{Binding ExportReportCommand}"
                                Cursor="Hand"/>
                    </StackPanel>

                    <!-- 状态显示 -->
                    <TextBlock Grid.Row="2" Grid.Column="0" Grid.ColumnSpan="4"
                               Text="准备生成报表"
                               FontSize="14"
                               Foreground="#666"
                               Margin="0,10,0,0"
                               TextWrapping="Wrap"/>
                </Grid>
            </Border>

            <!-- 报表预览区域 -->
            <Border Background="#FAFAFA"
                    CornerRadius="5"
                    Padding="20"
                    MinHeight="300">
                <StackPanel>
                    <TextBlock Text="📋 报表预览"
                               FontSize="16"
                               FontWeight="Bold"
                               Margin="0,0,0,15"/>
                    
                    <!-- 报表数据表格 -->
                    <DataGrid ItemsSource="{Binding ReportData}"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              RowBackground="White"
                              AlternatingRowBackground="#F8F9FA"
                              BorderThickness="1"
                              BorderBrush="#E0E0E0"
                              Height="250"
                              Margin="0,10,0,0">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ID" 
                                                Binding="{Binding Id}" 
                                                Width="80"/>
                            <DataGridTextColumn Header="名称" 
                                                Binding="{Binding Name}" 
                                                Width="150"/>
                            <DataGridTextColumn Header="类型" 
                                                Binding="{Binding Type}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="日期" 
                                                Binding="{Binding Date, StringFormat=\{0:yyyy-MM-dd\}}" 
                                                Width="120"/>
                            <DataGridTextColumn Header="数值" 
                                                Binding="{Binding Value}" 
                                                Width="100"/>
                            <DataGridTextColumn Header="状态" 
                                                Binding="{Binding Status}" 
                                                Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <TextBlock Text="{Binding ReportStatus}"
                               FontSize="14"
                               Foreground="#666"
                               Margin="0,15,0,0"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </Border>
</UserControl>
