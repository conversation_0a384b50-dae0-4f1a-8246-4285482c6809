using System;
using System.Globalization;
using System.Windows.Data;

namespace WPF_MVVM_Test.MVVM_View.Converters
{
    public class EditModeToSaveTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isEditMode)
            {
                return isEditMode ? "更新" : "保存";
            }
            return "保存";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}