<Window x:Class="WPF_MVVM_Test.MVVM_View.Material.MaterialAddDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Material"
        mc:Ignorable="d"
        Title="新增物料"
        Height="700"
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="新增物料"
                   FontSize="24"
                   FontWeight="Bold"
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center"/>

        <!-- 表单内容 -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel Margin="0,0,0,20">
                
                <!-- 基本信息 -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="基本信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,15"
                                   Foreground="#2E3440"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 物料编号 -->
                            <TextBox Grid.Row="0" Grid.Column="0"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="物料编号 *"
                                     Text="{Binding MaterialNumber, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 物料名称 -->
                            <TextBox Grid.Row="0" Grid.Column="2"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="物料名称 *"
                                     Text="{Binding MaterialName, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 规格型号 -->
                            <TextBox Grid.Row="1" Grid.Column="0"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="规格型号"
                                     Text="{Binding SpecificationModel, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 单位 -->
                            <TextBox Grid.Row="1" Grid.Column="2"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="单位"
                                     Text="{Binding Unit, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 物料类型 -->
                            <ComboBox Grid.Row="2" Grid.Column="0"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      materialDesign:HintAssist.Hint="物料类型"
                                      SelectedValue="{Binding MaterialType}"
                                      Margin="0,0,0,15"
                                      Height="60">
                                <ComboBoxItem Content="原材料" Tag="1"/>
                                <ComboBoxItem Content="半成品" Tag="2"/>
                                <ComboBoxItem Content="成品" Tag="3"/>
                            </ComboBox>

                            <!-- 物料属性 -->
                            <ComboBox Grid.Row="2" Grid.Column="2"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      materialDesign:HintAssist.Hint="物料属性"
                                      Text="{Binding MaterialProperty}"
                                      IsEditable="True"
                                      Margin="0,0,0,15"
                                      Height="60">
                                <ComboBoxItem Content="外购"/>
                                <ComboBoxItem Content="自制"/>
                                <ComboBoxItem Content="委外"/>
                            </ComboBox>

                            <!-- 物料分类 - SimpleTreeComboBox -->
                            <local:SimpleTreeComboBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3"
                                                      ItemsSource="{Binding MaterialCategories}"
                                                      SelectedValue="{Binding MaterialCategoryId, Mode=TwoWay}"
                                                      Margin="0,0,0,15"/>

                            <!-- 备用普通ComboBox（调试用）
                            <ComboBox Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="3"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      materialDesign:HintAssist.Hint="物料分类 * (测试版)"
                                      ItemsSource="{Binding MaterialCategories}"
                                      DisplayMemberPath="CategoryName"
                                      SelectedValuePath="Id"
                                      SelectedValue="{Binding MaterialCategoryId}"
                                      Margin="0,0,0,15"
                                      Height="60"/>
            
-->
                            <!-- 状态 -->
                            <ComboBox Grid.Row="4" Grid.Column="0"
                                      Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                      materialDesign:HintAssist.Hint="状态"
                                      Text="{Binding Status}"
                                      Margin="0,0,0,15"
                                      Height="60">
                                <ComboBoxItem Content="启用"/>
                                <ComboBoxItem Content="停用"/>
                            </ComboBox>

                            <!-- 生效单位 -->
                            <TextBox Grid.Row="4" Grid.Column="2"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="生效单位"
                                     Text="{Binding EffectiveUnit, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 库存信息 -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="库存信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,15"
                                   Foreground="#2E3440"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 库存上限 -->
                            <TextBox Grid.Row="0" Grid.Column="0"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="库存上限"
                                     Text="{Binding StockUpperLimit, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 库存下限 -->
                            <TextBox Grid.Row="0" Grid.Column="2"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="库存下限"
                                     Text="{Binding StockLowerLimit, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 预警天数 -->
                            <TextBox Grid.Row="1" Grid.Column="0"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="预警天数"
                                     Text="{Binding AlarmDays, UpdateSourceTrigger=PropertyChanged}"
                                     Margin="0,0,0,15"
                                     Height="60"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 价格信息 -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="价格信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,15"
                                   Foreground="#2E3440"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- 采购价格 -->
                            <TextBox Grid.Column="0"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="采购价格"
                                     Text="{Binding PurchasePrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                                     Margin="0,0,0,15"
                                     Height="60"/>

                            <!-- 销售价格 -->
                            <TextBox Grid.Column="2"
                                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                     materialDesign:HintAssist.Hint="销售价格"
                                     Text="{Binding SalesPrice, UpdateSourceTrigger=PropertyChanged, StringFormat=F2}"
                                     Margin="0,0,0,15"
                                     Height="60"/>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- 其他信息 -->
                <materialDesign:Card Margin="0,0,0,15" Padding="20">
                    <StackPanel>
                        <TextBlock Text="其他信息" 
                                   FontSize="16" 
                                   FontWeight="Bold" 
                                   Margin="0,0,0,15"
                                   Foreground="#2E3440"/>
                        
                        <!-- 生效日期 -->
                        <DatePicker Style="{StaticResource MaterialDesignOutlinedDatePicker}"
                                    materialDesign:HintAssist.Hint="生效日期"
                                    SelectedDate="{Binding EffectiveDate}"
                                    Margin="0,0,0,15"
                                    Height="60"/>

                        <!-- 备注 -->
                        <TextBox Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 materialDesign:HintAssist.Hint="备注"
                                 Text="{Binding Remarks, UpdateSourceTrigger=PropertyChanged}"
                                 AcceptsReturn="True"
                                 TextWrapping="Wrap"
                                 VerticalScrollBarVisibility="Auto"
                                 MinHeight="80"
                                 MaxHeight="120"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Command="{Binding CancelCommand}"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Content="取消"
                    Width="100"
                    Height="40"
                    Margin="0,0,15,0"/>
            
            <Button Command="{Binding SaveCommand}"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="保存"
                    Width="100"
                    Height="40"/>
        </StackPanel>
    </Grid>
</Window>