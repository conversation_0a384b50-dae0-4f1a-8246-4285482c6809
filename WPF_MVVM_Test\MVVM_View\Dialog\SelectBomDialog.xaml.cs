using System.Windows;
using WPF_MVVM_Test.MVVM_Model.Plan;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;

namespace WPF_MVVM_Test.MVVM_View.Dialog
{
    public partial class SelectBomDialog : Window
    {
        public BomModel? SelectedBom
        {
            get
            {
                if (DataContext is SelectBomViewModel viewModel)
                {
                    return viewModel.SelectedBom;
                }
                return null;
            }
        }

        public SelectBomDialog(ProductBomInfo productInfo)
        {
            InitializeComponent();
            DataContext = new SelectBomViewModel(productInfo);
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}