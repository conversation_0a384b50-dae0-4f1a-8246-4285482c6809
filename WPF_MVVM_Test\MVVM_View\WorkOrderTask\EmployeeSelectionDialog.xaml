<Window x:Class="WPF_MVVM_Test.MVVM_View.WorkOrderTask.EmployeeSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择其他成员"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize"
        Background="White">

    <Window.Resources>
        <!-- 搜索框样式 -->
        <Style x:Key="SearchBoxStyle" TargetType="TextBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="12,0"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <Grid>
                                <ScrollViewer x:Name="PART_ContentHost" 
                                              Margin="{TemplateBinding Padding}"
                                              VerticalAlignment="Center"/>
                                <TextBlock Text="请输入"
                                           Foreground="#BFBFBF"
                                           Margin="{TemplateBinding Padding}"
                                           VerticalAlignment="Center"
                                           IsHitTestVisible="False">
                                    <TextBlock.Style>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="Visibility" Value="Collapsed"/>
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Text, RelativeSource={RelativeSource TemplatedParent}}" Value="">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </TextBlock.Style>
                                </TextBlock>
                                <Button Content="🔍"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        HorizontalAlignment="Right"
                                        VerticalAlignment="Center"
                                        Margin="0,0,10,0"
                                        FontSize="16"
                                        Foreground="#1890FF"
                                        Command="{Binding SearchCommand}"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 员工项样式 -->
        <Style x:Key="EmployeeItemStyle" TargetType="CheckBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <Border Background="White"
                                BorderBrush="#F0F0F0"
                                BorderThickness="0,0,0,1"
                                Padding="16,12">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                
                                <CheckBox Grid.Column="0"
                                          IsChecked="{Binding IsSelected}"
                                          VerticalAlignment="Center"
                                          Margin="0,0,12,0"/>
                                
                                <TextBlock Grid.Column="1"
                                           Text="{Binding Name}"
                                           FontSize="14"
                                           VerticalAlignment="Center"/>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 已选项样式 -->
        <Style x:Key="SelectedItemStyle" TargetType="Border">
            <Setter Property="Background" Value="#F6F6F6"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="0,0,0,1"/>
            <Setter Property="Padding" Value="16,12"/>
        </Style>

        <!-- 删除按钮样式 -->
        <Style x:Key="RemoveButtonStyle" TargetType="Button">
            <Setter Property="Width" Value="20"/>
            <Setter Property="Height" Value="20"/>
            <Setter Property="Background" Value="#FF4D4F"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="10">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 主按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="36"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- 次要按钮样式 -->
        <Style x:Key="SecondaryButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="36"/>
            <Setter Property="MinWidth" Value="80"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#666666"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <TextBox Grid.Row="0"
                 Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                 Style="{StaticResource SearchBoxStyle}"
                 Margin="0,0,0,20"/>

        <!-- 主要内容区域 -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 左侧：班组一成员列表 -->
            <Border Grid.Column="0"
                    BorderBrush="#F0F0F0"
                    BorderThickness="1"
                    Margin="0,0,10,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="#FAFAFA"
                            BorderBrush="#F0F0F0"
                            BorderThickness="0,0,0,1"
                            Padding="16,12">
                        <TextBlock Text="班组一成员 (5/9人)"
                                   FontSize="14"
                                   FontWeight="Bold"/>
                    </Border>

                    <!-- 员工列表 -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding FilteredEmployees}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <CheckBox Style="{StaticResource EmployeeItemStyle}"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>

            <!-- 右侧：已选对象 -->
            <Border Grid.Column="1"
                    BorderBrush="#F0F0F0"
                    BorderThickness="1"
                    Margin="10,0,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- 标题 -->
                    <Border Grid.Row="0"
                            Background="#FAFAFA"
                            BorderBrush="#F0F0F0"
                            BorderThickness="0,0,0,1"
                            Padding="16,12">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Column="0"
                                       Text="{Binding SelectedCountText}"
                                       FontSize="14"
                                       FontWeight="Bold"/>
                            
                            <Button Grid.Column="1"
                                    Content="清空"
                                    Foreground="#1890FF"
                                    Background="Transparent"
                                    BorderThickness="0"
                                    FontSize="12"
                                    Command="{Binding ClearAllCommand}"/>
                        </Grid>
                    </Border>

                    <!-- 已选列表 -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto">
                        <ItemsControl ItemsSource="{Binding SelectedEmployees}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Style="{StaticResource SelectedItemStyle}">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <TextBlock Grid.Column="0"
                                                       Text="{Binding Name}"
                                                       FontSize="14"
                                                       VerticalAlignment="Center"/>
                                            
                                            <Button Grid.Column="1"
                                                    Content="×"
                                                    Style="{StaticResource RemoveButtonStyle}"
                                                    Command="{Binding DataContext.RemoveSelectedCommand, RelativeSource={RelativeSource AncestorType=Window}}"
                                                    CommandParameter="{Binding}"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,20,0,0">
            <Button Content="取消"
                    Style="{StaticResource SecondaryButtonStyle}"
                    Command="{Binding CancelCommand}"
                    Margin="0,0,12,0"/>
            <Button Content="确定"
                    Style="{StaticResource PrimaryButtonStyle}"
                    Command="{Binding ConfirmCommand}"/>
        </StackPanel>
    </Grid>
</Window>
