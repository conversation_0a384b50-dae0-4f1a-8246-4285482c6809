﻿using System.Collections.Generic;
using WPF_MVVM_Test.MVVM_Model.Bom.Bom;

namespace WPF_MVVM_Test.MVVM_Model.Bom
{
    /// <summary>
    /// BOM分页查询结果模型
    /// </summary>
    public class BomPagedResult
    {
        /// <summary>
        /// BOM数据列表
        /// </summary>
        public List<BomItem> Data { get; set; } = new List<BomItem>();
        
        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalCount { get; set; }
        
        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPage { get; set; }
        
        /// <summary>
        /// 是否成功
        /// </summary>
        public bool IsSuc { get; set; }
        
        /// <summary>
        /// 响应消息
        /// </summary>
        public string Msg { get; set; } = string.Empty;
    }
}