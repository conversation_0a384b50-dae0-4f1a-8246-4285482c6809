using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Process
{
    /// <summary>
    /// 工序物料模型
    /// </summary>
    public class ProcessStepMaterialModel : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _materialNumber = string.Empty;
        private string _materialName = string.Empty;
        private string _specificationModel = string.Empty;
        private string _unit = string.Empty;
        private int _materialType;
        private string _materialProperty = string.Empty;
        private string _status = string.Empty;
        private decimal _purchasePrice;
        private decimal _salesPrice;
        private string _remarks = string.Empty;

        /// <summary>
        /// 物料ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 物料编号
        /// </summary>
        [JsonPropertyName("materialNumber")]
        public string MaterialNumber
        {
            get => _materialNumber;
            set
            {
                if (_materialNumber != value)
                {
                    _materialNumber = value;
                    OnPropertyChanged(nameof(MaterialNumber));
                }
            }
        }

        /// <summary>
        /// 物料名称
        /// </summary>
        [JsonPropertyName("materialName")]
        public string MaterialName
        {
            get => _materialName;
            set
            {
                if (_materialName != value)
                {
                    _materialName = value;
                    OnPropertyChanged(nameof(MaterialName));
                }
            }
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonPropertyName("specificationModel")]
        public string SpecificationModel
        {
            get => _specificationModel;
            set
            {
                if (_specificationModel != value)
                {
                    _specificationModel = value;
                    OnPropertyChanged(nameof(SpecificationModel));
                }
            }
        }

        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }
        }

        /// <summary>
        /// 物料类型
        /// </summary>
        [JsonPropertyName("materialType")]
        public int MaterialType
        {
            get => _materialType;
            set
            {
                if (_materialType != value)
                {
                    _materialType = value;
                    OnPropertyChanged(nameof(MaterialType));
                }
            }
        }

        /// <summary>
        /// 物料属性
        /// </summary>
        [JsonPropertyName("materialProperty")]
        public string MaterialProperty
        {
            get => _materialProperty;
            set
            {
                if (_materialProperty != value)
                {
                    _materialProperty = value;
                    OnPropertyChanged(nameof(MaterialProperty));
                }
            }
        }

        /// <summary>
        /// 状态
        /// </summary>
        [JsonPropertyName("status")]
        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        /// <summary>
        /// 采购价格
        /// </summary>
        [JsonPropertyName("purchasePrice")]
        public decimal PurchasePrice
        {
            get => _purchasePrice;
            set
            {
                if (_purchasePrice != value)
                {
                    _purchasePrice = value;
                    OnPropertyChanged(nameof(PurchasePrice));
                }
            }
        }

        /// <summary>
        /// 销售价格
        /// </summary>
        [JsonPropertyName("salesPrice")]
        public decimal SalesPrice
        {
            get => _salesPrice;
            set
            {
                if (_salesPrice != value)
                {
                    _salesPrice = value;
                    OnPropertyChanged(nameof(SalesPrice));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remarks")]
        public string Remarks
        {
            get => _remarks;
            set
            {
                if (_remarks != value)
                {
                    _remarks = value;
                    OnPropertyChanged(nameof(Remarks));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 工序物料API响应模型
    /// </summary>
    public class ProcessStepMaterialApiResponse
    {
        [JsonPropertyName("data")]
        public List<ProcessStepMaterialModel> Data { get; set; } = new List<ProcessStepMaterialModel>();

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }
}