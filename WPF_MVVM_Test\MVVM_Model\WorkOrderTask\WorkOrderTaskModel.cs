using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.WorkOrderTask
{
    /// <summary>
    /// 工单任务数据模型 - 对应API接口的工单任务数据
    /// </summary>
    public class WorkOrderTaskModel : INotifyPropertyChanged
    {
        #region 私有字段

        private string _id;
        private int _index;
        private bool _isSelected;
        private string _taskNumber;
        private string _taskName;
        private string _stationName;
        private DateTime? _planStartTime;
        private DateTime? _actualStartTime;
        private string _planDuration;
        private DateTime? _actualEndTime;
        private string _actualDuration;
        private string _status;
        private string _remarks;

        // 新增字段
        private int _sequenceNumber;
        private string _processCode;
        private string _processName;
        private DateTime? _planEndTime;
        private int _planQuantity;
        private int _actualQuantity;

        #endregion

        #region 公共属性

        /// <summary>
        /// 主键ID
        /// </summary>
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 序号
        /// </summary>
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set => SetProperty(ref _isSelected, value);
        }

        /// <summary>
        /// 任务编号 - 如RWBH0215
        /// </summary>
        public string TaskNumber
        {
            get => _taskNumber;
            set => SetProperty(ref _taskNumber, value);
        }

        /// <summary>
        /// 任务名称 - 如生产工件任务2
        /// </summary>
        public string TaskName
        {
            get => _taskName;
            set => SetProperty(ref _taskName, value);
        }

        /// <summary>
        /// 站点名称 - 如站点一
        /// </summary>
        public string StationName
        {
            get => _stationName;
            set => SetProperty(ref _stationName, value);
        }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime? PlanStartTime
        {
            get => _planStartTime;
            set => SetProperty(ref _planStartTime, value);
        }

        /// <summary>
        /// 实际开工时间
        /// </summary>
        public DateTime? ActualStartTime
        {
            get => _actualStartTime;
            set => SetProperty(ref _actualStartTime, value);
        }

        /// <summary>
        /// 计划生产时长 - 如1小时
        /// </summary>
        public string PlanDuration
        {
            get => _planDuration;
            set => SetProperty(ref _planDuration, value);
        }

        /// <summary>
        /// 实际完工时间
        /// </summary>
        public DateTime? ActualEndTime
        {
            get => _actualEndTime;
            set => SetProperty(ref _actualEndTime, value);
        }

        /// <summary>
        /// 实际生产时长 - 如1小时30分钟
        /// </summary>
        public string ActualDuration
        {
            get => _actualDuration;
            set => SetProperty(ref _actualDuration, value);
        }

        /// <summary>
        /// 状态
        /// </summary>
        public string Status
        {
            get => _status;
            set
            {
                if (SetProperty(ref _status, value))
                {
                    OnPropertyChanged(nameof(StatusText));
                    OnPropertyChanged(nameof(StatusColor));
                    OnPropertyChanged(nameof(CanStart));
                    OnPropertyChanged(nameof(CanComplete));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks
        {
            get => _remarks;
            set => SetProperty(ref _remarks, value);
        }

        /// <summary>
        /// 序号
        /// </summary>
        public int SequenceNumber
        {
            get => _sequenceNumber;
            set => SetProperty(ref _sequenceNumber, value);
        }

        /// <summary>
        /// 工艺编号
        /// </summary>
        public string ProcessCode
        {
            get => _processCode;
            set => SetProperty(ref _processCode, value);
        }

        /// <summary>
        /// 工艺名称
        /// </summary>
        public string ProcessName
        {
            get => _processName;
            set => SetProperty(ref _processName, value);
        }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime? PlanEndTime
        {
            get => _planEndTime;
            set => SetProperty(ref _planEndTime, value);
        }

        /// <summary>
        /// 计划数量
        /// </summary>
        public int PlanQuantity
        {
            get => _planQuantity;
            set => SetProperty(ref _planQuantity, value);
        }

        /// <summary>
        /// 实际数量
        /// </summary>
        public int ActualQuantity
        {
            get => _actualQuantity;
            set => SetProperty(ref _actualQuantity, value);
        }

        #endregion

        #region 计算属性

        /// <summary>
        /// 状态显示文本
        /// </summary>
        public string StatusText => GetStatusText();

        /// <summary>
        /// 状态颜色
        /// </summary>
        public string StatusColor => GetStatusColor();

        /// <summary>
        /// 是否可以开工
        /// </summary>
        public bool CanStart => Status == "未派工" || Status == "已下达";

        /// <summary>
        /// 是否可以完工
        /// </summary>
        public bool CanComplete => Status == "进行中";

        #endregion

        #region 私有方法

        private string GetStatusText()
        {
            return Status switch
            {
                "未派工" => "未派工",
                "已下达" => "已下达",
                "进行中" => "进行中",
                "已完成" => "已完成",
                "已暂停" => "已暂停",
                "已关闭" => "已关闭",
                _ => Status ?? "未知"
            };
        }

        private string GetStatusColor()
        {
            return Status switch
            {
                "未派工" => "#8C8C8C", // 灰色
                "已下达" => "#1890FF", // 蓝色
                "进行中" => "#52C41A", // 绿色
                "已完成" => "#FA8C16", // 橙色
                "已暂停" => "#FF4D4F", // 红色
                "已关闭" => "#6C757D", // 深灰色
                _ => "#8C8C8C" // 默认灰色
            };
        }

        #endregion

        #region INotifyPropertyChanged实现

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        #endregion
    }
}
