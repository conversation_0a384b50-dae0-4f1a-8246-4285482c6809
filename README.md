# WPF学习项目集合

本项目包含了三个不同阶段的WPF学习项目，从基础的原生WPF到现代化的MVVM架构，展示了WPF开发的完整学习路径。

## 📁 项目结构

```
WpfTest/
├── WpfTest/           # 原生WPF测试项目
├── WpfDemo/           # MaterialDesign组件测试项目  
├── WPF_MVVM_Test/     # MVVM架构项目（重点）
└── README.md          # 项目说明文档
```

## 🚀 项目介绍

### 1. WpfTest - 原生WPF测试项目

**项目特点：**
- 使用原生WPF控件和样式
- 基础的事件驱动编程模式
- 简单的窗口和控件交互
- 适合WPF入门学习

**主要功能：**
- 基本控件使用示例
- 窗口间导航
- 简单的数据绑定
- 事件处理机制

**技术栈：**
- .NET 6.0
- WPF原生控件
- 传统的Code-Behind模式

### 2. WpfDemo - MaterialDesign组件测试项目

**项目特点：**
- 集成MaterialDesignInXamlToolkit
- 现代化的Material Design风格UI
- 丰富的动画效果和交互体验
- 响应式设计理念

**主要功能：**
- Material Design控件展示
- 主题切换功能
- 动画效果演示
- 现代化UI组件使用

**技术栈：**
- .NET 6.0
- MaterialDesignInXamlToolkit
- Material Design设计语言

### 3. WPF_MVVM_Test - MVVM架构项目 ⭐（重点项目）

这是本项目集合的核心项目，展示了现代WPF应用开发的最佳实践。

#### 🏗️ 架构设计

**MVVM架构模式：**
```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│      View       │◄──►│    ViewModel     │◄──►│      Model      │
│   (XAML + UI)   │    │ (业务逻辑+状态)   │    │   (数据模型)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

**项目结构：**
```
WPF_MVVM_Test/
├── MVVM_Model/              # 数据模型层
│   ├── Account.cs           # 用户账户模型
│   └── User.cs              # 用户信息模型
├── MVVM_View/               # 视图层
│   ├── UserControl/         # 页面组件
│   │   ├── HomePage.xaml    # 首页组件
│   │   ├── UserManagement.xaml      # 用户管理组件
│   │   ├── ReportManagement.xaml    # 报表管理组件
│   │   └── SystemSettings.xaml      # 系统设置组件
│   └── MainWindow.xaml      # 主窗口
├── MVVM_ViewModel/          # 视图模型层
│   ├── BaseViewModel.cs     # 基础ViewModel
│   ├── MainWindowViewModel.cs       # 主窗口ViewModel
│   └── ReportManagementViewModel.cs # 报表管理ViewModel
├── Services/                # 服务层
│   └── UserService.cs       # 用户服务
└── App.xaml                 # 应用程序入口
```

#### 🎯 核心特性

**1. 页面组件化设计**
- 每个功能页面都是独立的UserControl
- 支持页面间的解耦和复用
- 便于团队协作开发

**2. 数据绑定机制**
- 双向数据绑定
- 命令绑定（Command Pattern）
- 属性变更通知（INotifyPropertyChanged）

**3. 页面导航系统**
- 基于可见性控制的页面切换
- 流畅的页面切换体验
- 状态保持机制

**4. 服务层架构**
- HTTP客户端封装
- 异步数据操作
- 错误处理机制

#### 🔧 技术实现

**页面切换机制：**
```
用户点击菜单 → Command执行 → CurrentPage属性更新 
→ PropertyChanged事件 → 页面可见性重新计算 → UI自动更新
```

**数据流向：**
```
UI交互 → Command → ViewModel → Service → 数据源
数据源 → Service → ViewModel → PropertyChanged → UI更新
```

#### 📱 功能模块

**1. 首页模块**
- 系统概况展示
- 数据统计卡片
- 快速操作入口
- 系统状态监控

**2. 用户管理模块**
- 用户列表展示
- 用户信息CRUD操作
- 数据表格展示
- 测试数据填充

**3. 报表管理模块**
- 报表类型选择
- 日期范围筛选
- 报表生成和导出
- 报表预览功能

**4. 系统设置模块**
- 系统参数配置
- 主题设置
- 用户偏好设置
- 配置保存功能

#### 🎨 UI设计特点

**现代化界面：**
- 扁平化设计风格
- 统一的色彩方案
- 圆角和阴影效果
- 响应式布局

**交互体验：**
- 流畅的页面切换
- 直观的操作反馈
- 友好的错误提示
- 一致的操作模式

#### 🛠️ 开发亮点

**1. 架构优势**
- 职责分离清晰
- 代码可维护性高
- 易于单元测试
- 支持功能扩展

**2. 性能优化**
- 页面延迟加载
- 异步数据操作
- 内存管理优化
- UI线程保护

**3. 最佳实践**
- 遵循SOLID原则
- 使用设计模式
- 代码规范统一
- 注释文档完善

## 🚀 快速开始

### 环境要求
- Visual Studio 2022 或更高版本
- .NET 6.0 SDK
- Windows 10/11

### 运行步骤

1. **克隆项目**
```bash
git clone https://gitee.com/leafyl/wpf_-lxr.git
cd wpf_-lxr
```

2. **打开解决方案**
```bash
# 使用Visual Studio打开
start WpfTest.sln
```

3. **运行项目**
- 设置启动项目（推荐：WPF_MVVM_Test）
- 按F5运行或点击"开始调试"

## 📚 学习路径建议

### 初学者路径
1. **WpfTest** - 了解WPF基础概念和控件使用
2. **WpfDemo** - 学习现代化UI设计和第三方组件
3. **WPF_MVVM_Test** - 掌握MVVM架构和企业级开发

### 进阶开发者
- 直接学习 **WPF_MVVM_Test** 项目
- 重点理解MVVM架构模式
- 学习页面组件化设计思想
- 掌握现代WPF开发最佳实践

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 贡献方式
1. Fork本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👨‍💻 作者

**李新 (Leaf215Y)**
- 码云: [@leafyl](https://gitee.com/leafyl)
- 项目地址: [https://gitee.com/leafyl/wpf_-lxr](https://gitee.com/leafyl/wpf_-lxr)

## 🙏 致谢

感谢所有为WPF社区做出贡献的开发者们！

---

⭐ 如果这个项目对您有帮助，请给个Star支持一下！
