using System;
using System.Globalization;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;

namespace WPF_MVVM_Test.MVVM_View.Process_process
{
    /// <summary>
    /// ProcessRouteManagement.xaml 的交互逻辑
    /// </summary>
    public partial class ProcessRouteManagement : System.Windows.Controls.UserControl
    {
        public ProcessRouteManagement()
        {
            InitializeComponent();
        }
    }

    /// <summary>
    /// 行号转换器
    /// </summary>
    public class RowNumberConverter : IValueConverter
    {
        public static readonly RowNumberConverter Instance = new RowNumberConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is DataGridRow row)
            {
                var dataGrid = ItemsControl.ItemsControlFromItemContainer(row) as DataGrid;
                if (dataGrid != null)
                {
                    int index = dataGrid.ItemContainerGenerator.IndexFromContainer(row);
                    return (index + 1).ToString();
                }
            }
            return "1";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}