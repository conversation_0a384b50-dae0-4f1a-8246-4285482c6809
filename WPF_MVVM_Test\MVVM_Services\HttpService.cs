using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json;
using WPF_MVVM_Test.MVVM_Model.Common;

namespace WPF_MVVM_Test.MVVM_Services
{
    public class HttpService
    {
        private readonly HttpClient _httpClient;
        private readonly JsonSerializerOptions _jsonOptions;

        public HttpService()
        {
            _httpClient = new HttpClient();
            _httpClient.BaseAddress = new Uri("http://localhost:64922/");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
        }

        public async Task<ApiResponse<T>> GetAsync<T>(string url)
        {
            try
            {
                var response = await _httpClient.GetAsync(url);
                response.EnsureSuccessStatusCode();
                
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<T>>(content, _jsonOptions);
                
                return result;
            }
            catch (HttpRequestException ex)
            {
                return new ApiResponse<T>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"HTTP请求错误: {ex.Message}"
                };
            }
            catch (JsonException ex)
            {
                return new ApiResponse<T>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"JSON解析错误: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<T>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"未知错误: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<T>> PostAsync<T>(string url, object data)
        {
            try
            {
                var json = JsonSerializer.Serialize(data, _jsonOptions);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync(url, content);
                response.EnsureSuccessStatusCode();
                
                var responseContent = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<ApiResponse<T>>(responseContent, _jsonOptions);
                
                return result;
            }
            catch (HttpRequestException ex)
            {
                return new ApiResponse<T>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"HTTP请求错误: {ex.Message}"
                };
            }
            catch (JsonException ex)
            {
                return new ApiResponse<T>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"JSON解析错误: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                return new ApiResponse<T>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"未知错误: {ex.Message}"
                };
            }
        }

        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
} 