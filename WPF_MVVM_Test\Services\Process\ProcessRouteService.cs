using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading.Tasks;
using WPF_MVVM_Test.MVVM_Model.Process;

namespace WPF_MVVM_Test.Services.Process
{
    /// <summary>
    /// 工艺路线服务类
    /// </summary>
    public class ProcessRouteService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "http://localhost:64922/api/Process";

        public ProcessRouteService()
        {
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// 获取工艺路线分页数据
        /// </summary>
        /// <param name="status">状态筛选</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns></returns>
        public async Task<ProcessRouteApiResponse> GetProcessRoutePagedAsync(string status = "", int pageIndex = 1, int pageSize = 10)
        {
            try
            {
                var url = $"{_baseUrl}/GetProcessRoutePaged?pageIndex={pageIndex}&pageSize={pageSize}";
                if (!string.IsNullOrEmpty(status))
                {
                    url += $"&status={status}";
                }

                System.Diagnostics.Debug.WriteLine($"请求工艺路线数据，URL: {url}");

                var response = await _httpClient.GetAsync(url);

                if (response.IsSuccessStatusCode)
                {
                    var jsonString = await response.Content.ReadAsStringAsync();
                    
                    System.Diagnostics.Debug.WriteLine($"工艺路线API响应: {jsonString}");

                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    };

                    var result = JsonSerializer.Deserialize<ProcessRouteApiResponse>(jsonString, options);

                    if (result != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"工艺路线数据解析成功，数据数量: {result.Data?.Data?.Count ?? 0}");
                        return result;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine("工艺路线数据解析失败，返回空结果");
                        return new ProcessRouteApiResponse
                        {
                            IsSuc = false,
                            Code = 500,
                            Msg = "数据解析失败"
                        };
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"工艺路线API请求失败，状态码: {response.StatusCode}");
                    return new ProcessRouteApiResponse
                    {
                        IsSuc = false,
                        Code = (int)response.StatusCode,
                        Msg = $"请求失败: {response.StatusCode}"
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"工艺路线API请求异常: {ex.Message}");
                return new ProcessRouteApiResponse
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"请求异常: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}