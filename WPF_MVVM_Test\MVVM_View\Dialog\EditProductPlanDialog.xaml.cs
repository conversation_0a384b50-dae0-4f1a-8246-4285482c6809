using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;

namespace WPF_MVVM_Test.MVVM_View.Dialog
{
    /// <summary>
    /// EditProductPlanDialog.xaml 的交互逻辑
    /// </summary>
    public partial class EditProductPlanDialog : Window
    {
        public EditProductPlanDialog()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void Cancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}