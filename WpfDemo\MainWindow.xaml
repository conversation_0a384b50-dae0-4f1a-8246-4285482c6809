﻿<Window x:Class="WpfDemo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfDemo"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="Material Design 登录页面"
        Height="600"
        Width="800"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <!--添加组件的命名空间，配置窗体-->
    <Grid>
        <!-- 登录卡片 -->
        <materialDesign:Card
            Width="400"
            Height="450"
            Margin="20"
            VerticalAlignment="Center"
            HorizontalAlignment="Center">

            <!-- 卡片内容区域 -->
            <StackPanel Margin="30">
                <!-- 标题 -->
                <TextBlock
                    Text="用户登录"
                    FontSize="24"
                    FontWeight="Bold"
                    HorizontalAlignment="Center"
                    Margin="0,0,0,30"/>

                <!-- 用户名区域 -->
                <StackPanel Margin="0,0,0,20">
                    <TextBlock
                        Text="账号"
                        FontSize="14"
                        Margin="0,0,0,5"
                        Foreground="#666"/>
                    <TextBox
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        materialDesign:HintAssist.Hint="请输入用户名"
                        FontSize="16"
                        Height="50"
                        TextChanged="TextBox_TextChanged"/>
                </StackPanel>

                <!-- 密码区域 -->
                <StackPanel Margin="0,0,0,30">
                    <TextBlock
                        Text="密码"
                        FontSize="14"
                        Margin="0,0,0,5"
                        Foreground="#666"/>
                    <PasswordBox
                        Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                        materialDesign:HintAssist.Hint="请输入密码"
                        FontSize="16"
                        Height="50"/>
                </StackPanel>

                <!-- 登录按钮 -->
                <Button
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Content="登录"
                    Height="40"
                    FontSize="16"
                    Foreground="White"
                    Cursor="Hand"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>
