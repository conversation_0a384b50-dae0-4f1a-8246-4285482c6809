﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WPF_MVVM_Test.MVVM_Model
{
    /// <summary>
    /// 用户账户模型类 - MVVM模式中的Model层
    /// 作用：定义用户账户的数据结构
    /// 为什么这样设计：
    /// 1. Model层只负责数据结构，不包含业务逻辑
    /// 2. 纯粹的数据载体，便于数据传输和存储
    /// 3. 与界面和业务逻辑分离，符合MVVM的分层原则
    /// 4. 可以被多个ViewModel复用，提高代码重用性
    /// </summary>
    public class Account
    {
        /// <summary>
        /// 用户名
        /// 为什么使用string.Empty作为默认值：
        /// 1. 避免null引用异常
        /// 2. 确保属性始终有值，便于后续的字符串操作
        /// 3. 在UI绑定时不会显示null，而是显示空字符串
        /// </summary>
        public string AccountName { get; set; } = string.Empty;

        /// <summary>
        /// 密码
        /// 为什么使用string.Empty作为默认值：
        /// 1. 与用户名保持一致的初始化方式
        /// 2. 避免在密码验证时出现null值比较的问题
        /// 3. 简化后续的业务逻辑处理
        /// 注意：在实际项目中，密码应该进行加密处理，不应该以明文形式存储
        /// </summary>
        public string AccountPwd { get; set; } = string.Empty;
    }
}
