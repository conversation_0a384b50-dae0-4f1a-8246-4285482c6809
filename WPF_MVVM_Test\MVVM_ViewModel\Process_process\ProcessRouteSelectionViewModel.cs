using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.Process;
using WPF_MVVM_Test.MVVM_ViewModel;
using WPF_MVVM_Test.Services.Process;

namespace WPF_MVVM_Test.MVVM_ViewModel.Process_process
{
    /// <summary>
    /// 工艺路线选择对话框ViewModel
    /// </summary>
    public class ProcessRouteSelectionViewModel : BaseViewModel
    {
        private readonly ProcessRouteService _processRouteService;
        private ObservableCollection<ProcessRouteSelectionItem> _processRoutes;
        private string _searchKeyword = "";
        private int _currentPage = 1;
        private int _totalPages = 1;
        private int _pageSize = 10;
        private int _totalItems = 0;
        private bool _isLoading;

        public ProcessRouteSelectionViewModel()
        {
            _processRouteService = new ProcessRouteService();
            _processRoutes = new ObservableCollection<ProcessRouteSelectionItem>();

            // 初始化命令
            SearchCommand = new ProcessRouteAsyncCommand(async () => await SearchAsync());
            FirstPageCommand = new ProcessRouteAsyncCommand(async () => await GoToFirstPageAsync(), () => CanGoToFirstPage);
            PreviousPageCommand = new ProcessRouteAsyncCommand(async () => await GoToPreviousPageAsync(), () => CanGoToPreviousPage);
            NextPageCommand = new ProcessRouteAsyncCommand(async () => await GoToNextPageAsync(), () => CanGoToNextPage);
            LastPageCommand = new ProcessRouteAsyncCommand(async () => await GoToLastPageAsync(), () => CanGoToLastPage);

            // 加载数据
            _ = LoadProcessRoutesAsync();
        }

        #region 属性

        /// <summary>
        /// 工艺路线列表
        /// </summary>
        public ObservableCollection<ProcessRouteSelectionItem> ProcessRoutes
        {
            get => _processRoutes;
            set => SetProperty(ref _processRoutes, value);
        }

        /// <summary>
        /// 搜索关键词
        /// </summary>
        public string SearchKeyword
        {
            get => _searchKeyword;
            set => SetProperty(ref _searchKeyword, value);
        }

        /// <summary>
        /// 当前页码
        /// </summary>
        public int CurrentPage
        {
            get => _currentPage;
            set
            {
                if (SetProperty(ref _currentPage, value))
                {
                    OnPropertyChanged(nameof(PageInfo));
                    UpdatePageCommands();
                }
            }
        }

        /// <summary>
        /// 总页数
        /// </summary>
        public int TotalPages
        {
            get => _totalPages;
            set
            {
                if (SetProperty(ref _totalPages, value))
                {
                    OnPropertyChanged(nameof(PageInfo));
                    UpdatePageCommands();
                }
            }
        }

        /// <summary>
        /// 总记录数
        /// </summary>
        public int TotalItems
        {
            get => _totalItems;
            set
            {
                if (SetProperty(ref _totalItems, value))
                {
                    OnPropertyChanged(nameof(PageInfo));
                }
            }
        }

        /// <summary>
        /// 是否正在加载
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// 分页信息
        /// </summary>
        public string PageInfo => $"共 {TotalItems} 条";

        #endregion

        #region 命令

        public ICommand SearchCommand { get; }
        public ICommand FirstPageCommand { get; }
        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand LastPageCommand { get; }

        #endregion

        #region 分页相关

        private bool CanGoToFirstPage => CurrentPage > 1;
        private bool CanGoToPreviousPage => CurrentPage > 1;
        private bool CanGoToNextPage => CurrentPage < TotalPages;
        private bool CanGoToLastPage => CurrentPage < TotalPages;

        private async Task GoToFirstPageAsync()
        {
            CurrentPage = 1;
            await LoadProcessRoutesAsync();
        }

        private async Task GoToPreviousPageAsync()
        {
            if (CurrentPage > 1)
            {
                CurrentPage--;
                await LoadProcessRoutesAsync();
            }
        }

        private async Task GoToNextPageAsync()
        {
            if (CurrentPage < TotalPages)
            {
                CurrentPage++;
                await LoadProcessRoutesAsync();
            }
        }

        private async Task GoToLastPageAsync()
        {
            CurrentPage = TotalPages;
            await LoadProcessRoutesAsync();
        }

        private void UpdatePageCommands()
        {
            (FirstPageCommand as ProcessRouteAsyncCommand)?.RaiseCanExecuteChanged();
            (PreviousPageCommand as ProcessRouteAsyncCommand)?.RaiseCanExecuteChanged();
            (NextPageCommand as ProcessRouteAsyncCommand)?.RaiseCanExecuteChanged();
            (LastPageCommand as ProcessRouteAsyncCommand)?.RaiseCanExecuteChanged();
        }

        #endregion

        #region 命令执行方法

        /// <summary>
        /// 加载工艺路线数据
        /// </summary>
        private async Task LoadProcessRoutesAsync()
        {
            try
            {
                IsLoading = true;
                System.Diagnostics.Debug.WriteLine($"开始加载工艺路线数据，页码: {CurrentPage}，搜索关键词: {SearchKeyword}");

                var response = await _processRouteService.GetProcessRoutePagedAsync(
                    status: "",
                    pageIndex: CurrentPage,
                    pageSize: _pageSize
                );

                if (response.IsSuc)
                {
                    System.Diagnostics.Debug.WriteLine($"工艺路线数据加载成功，数据数量: {response.Data.Data.Count}");

                    ProcessRoutes.Clear();
                    foreach (var item in response.Data.Data)
                    {
                        ProcessRoutes.Add(new ProcessRouteSelectionItem(item));
                    }

                    TotalItems = response.Data.TotalCount;
                    TotalPages = response.Data.TotalPage;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"工艺路线数据加载失败: {response.Msg}，使用测试数据");
                    LoadTestData();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"工艺路线数据加载异常: {ex.Message}，使用测试数据");
                LoadTestData();
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// 加载测试数据
        /// </summary>
        private void LoadTestData()
        {
            ProcessRoutes.Clear();

            var testData = new[]
            {
                new ProcessRoute
                {
                    Id = "9cf33499-8615-4ac8-8df4-9275db039d0b",
                    ProcessRouteNumber = "GY20241201002",
                    ProcessRouteName = "改良产品工艺路线",
                    Status = "启用",
                    Description = "用于改良产品的生产工艺路线，包含预处理、主加工、精加工、检验、包装等主要工序",
                    Remark = "优化版本，加入了更多的精加工步骤以提高产品质量",
                    IsActive = true,
                    VersionDescription = "V1.1 优化版本 - 增加精加工步骤",
                    PreviousVersionId = "d5c88c60-5a7f-4c3b-adc7-34f017daf300",
                    ProcessStepCount = 3
                },
                new ProcessRoute
                {
                    Id = "b00fe53d-9eb2-4938-87d4-7a45755dcd0b",
                    ProcessRouteNumber = "GY20241201001",
                    ProcessRouteName = "新产品工艺路线",
                    Status = "启用",
                    Description = "用于新产品生产的标准工艺路线，包含预处理、加工、检验、包装等主要工序",
                    Remark = "初始版本，后续根据生产情况进行优化调整",
                    IsActive = true,
                    VersionDescription = "V1.0 初始版本 - 基础工艺流程",
                    PreviousVersionId = null,
                    ProcessStepCount = 5
                },
                new ProcessRoute
                {
                    Id = "c1d2e3f4-g5h6-7890-1234-567890abcdef",
                    ProcessRouteNumber = "GY20241201003",
                    ProcessRouteName = "精密加工工艺路线",
                    Status = "启用",
                    Description = "用于精密零件加工的专用工艺路线，包含精密切削、抛光、检测等工序",
                    Remark = "适用于高精度要求的产品",
                    IsActive = true,
                    VersionDescription = "V1.0 初始版本 - 精密加工流程",
                    PreviousVersionId = null,
                    ProcessStepCount = 4
                }
            };

            foreach (var item in testData)
            {
                ProcessRoutes.Add(new ProcessRouteSelectionItem(item));
            }

            TotalItems = ProcessRoutes.Count;
            TotalPages = 1;
        }

        /// <summary>
        /// 执行搜索
        /// </summary>
        private async Task SearchAsync()
        {
            CurrentPage = 1;
            await LoadProcessRoutesAsync();
        }

        #endregion

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _processRouteService?.Dispose();
            }
            base.Dispose(disposing);
        }
    }

    /// <summary>
    /// 工艺路线选择项
    /// </summary>
    public class ProcessRouteSelectionItem : ProcessRoute
    {
        private bool _isSelected;

        public ProcessRouteSelectionItem(ProcessRoute processRoute)
        {
            Id = processRoute.Id;
            ProcessRouteNumber = processRoute.ProcessRouteNumber;
            ProcessRouteName = processRoute.ProcessRouteName;
            Status = processRoute.Status;
            Description = processRoute.Description;
            Remark = processRoute.Remark;
            IsActive = processRoute.IsActive;
            VersionDescription = processRoute.VersionDescription;
            PreviousVersionId = processRoute.PreviousVersionId;
            ProcessStepCount = processRoute.ProcessStepCount;
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }
    }

    /// <summary>
    /// 工艺路线异步Command实现
    /// </summary>
    public class ProcessRouteAsyncCommand : ICommand
    {
        private readonly Func<Task> _executeAsync;
        private readonly Func<bool>? _canExecute;

        public ProcessRouteAsyncCommand(Func<Task> executeAsync, Func<bool>? canExecute = null)
        {
            _executeAsync = executeAsync ?? throw new ArgumentNullException(nameof(executeAsync));
            _canExecute = canExecute;
        }

        public event EventHandler? CanExecuteChanged;

        public bool CanExecute(object? parameter)
        {
            return _canExecute?.Invoke() ?? true;
        }

        public async void Execute(object? parameter)
        {
            await _executeAsync();
        }

        public void RaiseCanExecuteChanged()
        {
            CanExecuteChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}