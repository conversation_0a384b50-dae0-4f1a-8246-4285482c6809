<Window x:Class="WPF_MVVM_Test.MVVM_View.WorkOrderTask.DispatchDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="批量派工"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <Style x:Key="InputStyle" TargetType="FrameworkElement">
            <Setter Property="Height" Value="32"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="36"/>
            <Setter Property="Width" Value="100"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="批量派工"
                   FontSize="18"
                   FontWeight="Bold"
                   Margin="0,0,0,20"/>

        <!-- 派工信息 -->
        <Border Grid.Row="1"
                BorderBrush="#E9ECEF"
                BorderThickness="1"
                Padding="15"
                Margin="0,0,0,20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- 班组 -->
                <TextBlock Grid.Row="0" Grid.Column="0"
                           Text="班组："
                           Style="{StaticResource LabelStyle}"/>
                <ComboBox Grid.Row="0" Grid.Column="1"
                          ItemsSource="{Binding TeamOptions}"
                          SelectedItem="{Binding SelectedTeam}"
                          Style="{StaticResource InputStyle}"/>

                <!-- 负责人 -->
                <TextBlock Grid.Row="0" Grid.Column="2"
                           Text="负责人："
                           Style="{StaticResource LabelStyle}"/>
                <ComboBox Grid.Row="0" Grid.Column="3"
                          ItemsSource="{Binding SupervisorOptions}"
                          SelectedItem="{Binding SelectedSupervisor}"
                          Style="{StaticResource InputStyle}"/>

                <!-- 质量部门 -->
                <TextBlock Grid.Row="1" Grid.Column="0"
                           Text="质量部门："
                           Style="{StaticResource LabelStyle}"/>
                <ComboBox Grid.Row="1" Grid.Column="1"
                          ItemsSource="{Binding QualityDepartmentOptions}"
                          SelectedItem="{Binding SelectedQualityDepartment}"
                          Style="{StaticResource InputStyle}"/>

                <!-- 质量人员 -->
                <TextBlock Grid.Row="1" Grid.Column="2"
                           Text="质量人员："
                           Style="{StaticResource LabelStyle}"/>
                <ComboBox Grid.Row="1" Grid.Column="3"
                          ItemsSource="{Binding QualityPersonnelOptions}"
                          SelectedItem="{Binding SelectedQualityPersonnel}"
                          Style="{StaticResource InputStyle}"/>

                <!-- 其他成员 -->
                <TextBlock Grid.Row="2" Grid.Column="0"
                           Text="其他成员："
                           Style="{StaticResource LabelStyle}"/>
                <StackPanel Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="3"
                            Orientation="Horizontal">
                    <TextBox Text="{Binding OtherMembers}"
                             Width="300"
                             Style="{StaticResource InputStyle}"/>
                    <Button Content="选择人员"
                            Command="{Binding SelectMembersCommand}"
                            Style="{StaticResource ButtonStyle}"
                            Background="#1890FF"
                            Foreground="White"
                            Margin="10,0,0,0"/>
                </StackPanel>

                <!-- 备注 -->
                <TextBlock Grid.Row="3" Grid.Column="0"
                           Text="备注："
                           Style="{StaticResource LabelStyle}"/>
                <TextBox Grid.Row="3" Grid.Column="1" Grid.ColumnSpan="3"
                         Text="{Binding Notes}"
                         Height="60"
                         TextWrapping="Wrap"
                         AcceptsReturn="True"
                         VerticalScrollBarVisibility="Auto"
                         Margin="0,0,0,10"/>

                <!-- 任务列表 -->
                <TextBlock Grid.Row="4" Grid.Column="0" Grid.ColumnSpan="4"
                           Text="待派工任务列表："
                           Style="{StaticResource LabelStyle}"
                           Margin="0,10,0,5"/>

                <DataGrid Grid.Row="5" Grid.Column="0" Grid.ColumnSpan="4"
                          ItemsSource="{Binding SelectedTasks}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserResizeRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          IsReadOnly="True"
                          Background="White"
                          RowBackground="White"
                          AlternatingRowBackground="#F8F9FA"
                          BorderBrush="#E9ECEF"
                          BorderThickness="1"
                          MaxHeight="200">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="任务编号" Binding="{Binding TaskNumber}" Width="120"/>
                        <DataGridTextColumn Header="任务名称" Binding="{Binding TaskName}" Width="150"/>
                        <DataGridTextColumn Header="站点名称" Binding="{Binding StationName}" Width="100"/>
                        <DataGridTextColumn Header="计划开工时间" Width="140">
                            <DataGridTextColumn.Binding>
                                <Binding Path="PlanStartTime" StringFormat="yyyy-MM-dd HH:mm"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- 派工信息摘要 -->
        <Border Grid.Row="2"
                Background="#F8F9FA"
                Padding="15"
                Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="{Binding SummaryText}"
                           FontWeight="Bold"
                           Foreground="#1890FF"/>
            </StackPanel>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="3"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right">
            <Button Content="确认派工"
                    Command="{Binding ConfirmCommand}"
                    Style="{StaticResource ButtonStyle}"
                    Background="#52C41A"
                    Foreground="White"/>
            <Button Content="取消"
                    Command="{Binding CancelCommand}"
                    Style="{StaticResource ButtonStyle}"
                    Background="#6C757D"
                    Foreground="White"/>
        </StackPanel>
    </Grid>
</Window>
