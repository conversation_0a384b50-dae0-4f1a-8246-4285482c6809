<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Bom.BomAddPage"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Bom"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.Bom"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000">

    <UserControl.Resources>
        <local:RowIndexConverter x:Key="RowIndexConverter"/>
    </UserControl.Resources>

    <UserControl.DataContext>
        <vm:BomAddViewModel/>
    </UserControl.DataContext>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 基础信息 -->
            <GroupBox Header="基础信息" Grid.Row="0" Style="{StaticResource MaterialDesignCardGroupBox}">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- BOM编号 -->
                    <TextBox Grid.Row="0" Grid.Column="0" 
                             Text="{Binding BomNumber, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="* BOM编号" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                    
                    <!-- 系统编号（只读） -->
                    <TextBox Grid.Row="0" Grid.Column="1" 
                             Text="{Binding SystemNumber}"
                             materialDesign:HintAssist.Hint="系统编号" 
                             IsReadOnly="True" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                    
                    <!-- 默认BOM选择 -->
                    <StackPanel Grid.Row="0" Grid.Column="2" Orientation="Horizontal" VerticalAlignment="Center">
                        <TextBlock Text="* 默认BOM" VerticalAlignment="Center" Margin="5"/>
                        <RadioButton Content="是" 
                                     IsChecked="{Binding IsDefaultBom}" 
                                     GroupName="DefaultBom" 
                                     Margin="5"/>
                        <RadioButton Content="否" 
                                     GroupName="DefaultBom" 
                                     Margin="5"/>
                    </StackPanel>

                    <!-- BOM版本（只读，从产品选择后自动填充） -->
                    <TextBox Grid.Row="1" Grid.Column="0" 
                             Text="{Binding BomVersion}"
                             materialDesign:HintAssist.Hint="BOM版本" 
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                    
                    <!-- 产品名称（点击弹出选择框） -->
                    <Grid Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="2" Margin="5">
                        <TextBox Text="{Binding ProductName}"
                                 materialDesign:HintAssist.Hint="* 产品名称" 
                                 IsReadOnly="True"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"/>
                        <Button HorizontalAlignment="Right" 
                                VerticalAlignment="Center"
                                Margin="0,0,10,0"
                                Style="{StaticResource MaterialDesignIconButton}"
                                Command="{Binding SelectProductCommand}"
                                ToolTip="选择产品">
                            <materialDesign:PackIcon Kind="Magnify" Width="20" Height="20"/>
                        </Button>
                    </Grid>

                    <!-- 产品编号（只读，从产品选择后自动填充） -->
                    <TextBox Grid.Row="2" Grid.Column="0" 
                             Text="{Binding ProductNumber}"
                             materialDesign:HintAssist.Hint="产品编号" 
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                    
                    <!-- 规格型号（只读，从产品选择后自动填充） -->
                    <TextBox Grid.Row="2" Grid.Column="1" 
                             Text="{Binding Specification}"
                             materialDesign:HintAssist.Hint="规格型号" 
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                    
                    <!-- 单位（只读，从产品选择后自动填充） -->
                    <TextBox Grid.Row="2" Grid.Column="2" 
                             Text="{Binding Unit}"
                             materialDesign:HintAssist.Hint="单位" 
                             IsReadOnly="True"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                    
                    <!-- 日产量 -->
                    <TextBox Grid.Row="2" Grid.Column="3" 
                             Text="{Binding DailyOutput, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="日产量" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>

                    <!-- 备注 -->
                    <TextBox Grid.Row="3" Grid.ColumnSpan="4" 
                             Text="{Binding Remark, UpdateSourceTrigger=PropertyChanged}"
                             materialDesign:HintAssist.Hint="备注" 
                             AcceptsReturn="True" 
                             TextWrapping="Wrap" 
                             Height="80" 
                             Style="{StaticResource MaterialDesignOutlinedTextBox}" 
                             Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- 物料配件 -->
            <GroupBox Header="物料配件" Grid.Row="1" Margin="0,20,0,0" Style="{StaticResource MaterialDesignCardGroupBox}">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <StackPanel Orientation="Horizontal" Margin="5">
                        <Button Content="添加" 
                                Style="{StaticResource MaterialDesignRaisedButton}" 
                                Command="{Binding AddMaterialCommand}"
                                Margin="5"/>
                        <Button Content="移除" 
                                Style="{StaticResource MaterialDesignOutlinedButton}" 
                                Command="{Binding RemoveMaterialCommand}"
                                Margin="5"/>
                    </StackPanel>
                    <DataGrid Grid.Row="1" 
                              Margin="5" 
                              AutoGenerateColumns="False" 
                              CanUserAddRows="False"
                              ItemsSource="{Binding MaterialItems}"
                              SelectedItem="{Binding SelectedMaterialItem}">
                        <DataGrid.Columns>
                            <DataGridCheckBoxColumn Header="" 
                                                    Binding="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                                    Width="50"/>
                            <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True">
                                <DataGridTextColumn.Binding>
                                    <Binding Path="." Converter="{StaticResource RowIndexConverter}"/>
                                </DataGridTextColumn.Binding>
                            </DataGridTextColumn>
                            <DataGridTextColumn Header="物料名称" 
                                                Binding="{Binding MaterialName}" 
                                                Width="150" 
                                                IsReadOnly="True"/>
                            <DataGridTextColumn Header="物料编号" 
                                                Binding="{Binding MaterialNumber}" 
                                                Width="120" 
                                                IsReadOnly="True"/>
                            <DataGridTextColumn Header="规格型号" 
                                                Binding="{Binding SpecificationModel}" 
                                                Width="120" 
                                                IsReadOnly="True"/>
                            <DataGridTextColumn Header="单位" 
                                                Binding="{Binding Unit}" 
                                                Width="80" 
                                                IsReadOnly="True"/>
                            <DataGridTemplateColumn Header="数量" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBox Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                                 Style="{StaticResource MaterialDesignTextBox}"
                                                 HorizontalAlignment="Stretch"
                                                 VerticalAlignment="Center"
                                                 TextAlignment="Center"
                                                 Margin="5"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Header="损耗率(%)" Width="100">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBox Text="{Binding LossRate, UpdateSourceTrigger=PropertyChanged}"
                                                 Style="{StaticResource MaterialDesignTextBox}"
                                                 HorizontalAlignment="Stretch"
                                                 VerticalAlignment="Center"
                                                 TextAlignment="Center"
                                                 Margin="5"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                            <DataGridTemplateColumn Header="操作" Width="80">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <Button Content="移除" 
                                                Style="{StaticResource MaterialDesignFlatButton}"
                                                Command="{Binding DataContext.RemoveSingleMaterialCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </GroupBox>

            <!-- 工艺路线 -->
            <GroupBox Header="工艺路线" Grid.Row="2" Margin="0,20,0,0" Style="{StaticResource MaterialDesignCardGroupBox}">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Content="设置" 
                                Style="{StaticResource MaterialDesignRaisedButton}" 
                                Command="{Binding SelectProcessRouteCommand}"
                                Margin="5"/>
                        <Button Content="清除" 
                                Style="{StaticResource MaterialDesignOutlinedButton}" 
                                Command="{Binding ClearProcessRouteCommand}"
                                Margin="5"/>
                    </StackPanel>
                    <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="5">
                        <TextBlock VerticalAlignment="Center" Margin="5">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="工艺路线名称: {0}">
                                    <Binding Path="ProcessRouteName" TargetNullValue="未选择"/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                        <TextBlock VerticalAlignment="Center" Margin="5,0,0,0">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="工艺路线编号: {0}">
                                    <Binding Path="ProcessRouteNumber" TargetNullValue=""/>
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </StackPanel>
                    
                    <!-- 工艺步骤流程显示 -->
                    <ScrollViewer Grid.Row="2" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Disabled" Margin="5">
                        <ItemsControl ItemsSource="{Binding ProcessStepList}">
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Orientation="Horizontal"/>
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                        <!-- 工序步骤卡片 - 可点击 -->
                                        <Button Style="{StaticResource MaterialDesignFlatButton}"
                                                Padding="0"
                                                Command="{Binding DataContext.SelectProcessStepCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}">
                                            <materialDesign:Card Padding="15,8" 
                                                                 Margin="5"
                                                                 UniformCornerRadius="20">
                                                <materialDesign:Card.Style>
                                                    <Style TargetType="materialDesign:Card">
                                                        <Setter Property="Background" Value="#E3F2FD"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                                                <Setter Property="Background" Value="#2196F3"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </materialDesign:Card.Style>
                                                <TextBlock Text="{Binding ProcessStepName}"
                                                           FontWeight="Medium"
                                                           FontSize="13"
                                                           HorizontalAlignment="Center"
                                                           VerticalAlignment="Center">
                                                    <TextBlock.Style>
                                                        <Style TargetType="TextBlock">
                                                            <Setter Property="Foreground" Value="#1976D2"/>
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsSelected}" Value="True">
                                                                    <Setter Property="Foreground" Value="White"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </TextBlock.Style>
                                                </TextBlock>
                                            </materialDesign:Card>
                                        </Button>
                                        
                                        <!-- 箭头连接线 -->
                                        <materialDesign:PackIcon Kind="ChevronRight" 
                                                                 VerticalAlignment="Center" 
                                                                 Margin="5,0"
                                                                 Foreground="#90A4AE"
                                                                 Width="20"
                                                                 Height="20"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>

                    <!-- 工序物料显示区域 -->
                    <Grid Grid.Row="3" Margin="5">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <!-- 当前选中工序标题 -->
                        <TextBlock Grid.Row="0" 
                                   Text="{Binding CurrentProcessStepName}"
                                   FontSize="14"
                                   FontWeight="Bold"
                                   Margin="0,10,0,10"
                                   Foreground="#1976D2"/>
                        
                        <!-- 工序物料数据表格 -->
                        <DataGrid Grid.Row="1" 
                                  ItemsSource="{Binding ProcessStepMaterialList}"
                                  AutoGenerateColumns="False" 
                                  CanUserAddRows="False"
                                  CanUserDeleteRows="False"
                                  CanUserReorderColumns="False"
                                  CanUserResizeRows="False"
                                  HeadersVisibility="Column"
                                  GridLinesVisibility="All"
                                  AlternatingRowBackground="#F8F8F8"
                                  RowBackground="White"
                                  BorderBrush="#E0E0E0"
                                  BorderThickness="1"
                                  FontSize="12">
                            <DataGrid.Columns>
                                <!-- 序号 -->
                                <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True">
                                    <DataGridTextColumn.Binding>
                                        <Binding Path="." Converter="{StaticResource RowIndexConverter}"/>
                                    </DataGridTextColumn.Binding>
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 物料编号 -->
                                <DataGridTextColumn Header="物料编号" 
                                                    Binding="{Binding MaterialNumber}" 
                                                    Width="120"
                                                    IsReadOnly="True">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 物料名称 -->
                                <DataGridTextColumn Header="物料名称" 
                                                    Binding="{Binding MaterialName}" 
                                                    Width="150"
                                                    IsReadOnly="True">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 规格型号 -->
                                <DataGridTextColumn Header="规格型号" 
                                                    Binding="{Binding SpecificationModel}" 
                                                    Width="120"
                                                    IsReadOnly="True">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 单位 -->
                                <DataGridTextColumn Header="单位" 
                                                    Binding="{Binding Unit}" 
                                                    Width="80"
                                                    IsReadOnly="True">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 物料属性 -->
                                <DataGridTextColumn Header="物料属性" 
                                                    Binding="{Binding MaterialProperty}" 
                                                    Width="80"
                                                    IsReadOnly="True">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Center"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>

                                <!-- 状态 -->
                                <DataGridTemplateColumn Header="状态" Width="80">
                                    <DataGridTemplateColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTemplateColumn.HeaderStyle>
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <Border CornerRadius="10"
                                                    Padding="8,2"
                                                    HorizontalAlignment="Center">
                                                <Border.Style>
                                                    <Style TargetType="Border">
                                                        <Setter Property="Background" Value="#4CAF50"/>
                                                        <Style.Triggers>
                                                            <DataTrigger Binding="{Binding Status}" Value="停用">
                                                                <Setter Property="Background" Value="#F44336"/>
                                                            </DataTrigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </Border.Style>
                                                <TextBlock Text="{Binding Status}"
                                                           Foreground="White"
                                                           FontSize="10"
                                                           FontWeight="Bold"
                                                           HorizontalAlignment="Center"/>
                                            </Border>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>

                                <!-- 备注 -->
                                <DataGridTextColumn Header="备注" 
                                                    Binding="{Binding Remarks}" 
                                                    Width="*"
                                                    IsReadOnly="True">
                                    <DataGridTextColumn.HeaderStyle>
                                        <Style TargetType="DataGridColumnHeader">
                                            <Setter Property="Background" Value="#E8F5E8"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                            <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        </Style>
                                    </DataGridTextColumn.HeaderStyle>
                                    <DataGridTextColumn.ElementStyle>
                                        <Style TargetType="TextBlock">
                                            <Setter Property="HorizontalAlignment" Value="Left"/>
                                            <Setter Property="VerticalAlignment" Value="Center"/>
                                            <Setter Property="TextWrapping" Value="Wrap"/>
                                            <Setter Property="Margin" Value="5"/>
                                        </Style>
                                    </DataGridTextColumn.ElementStyle>
                                </DataGridTextColumn>
                            </DataGrid.Columns>
                            
                            <!-- 无数据时显示提示 -->
                            <DataGrid.Style>
                                <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ProcessStepMaterialList.Count}" Value="0">
                                            <Setter Property="Background" Value="Transparent"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.Style>
                        </DataGrid>
                        
                        <!-- 无数据提示 -->
                        <TextBlock Grid.Row="1"
                                   Text="暂无物料数据，请选择工序查看对应物料"
                                   FontSize="14"
                                   Foreground="#999"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding ProcessStepMaterialList.Count}" Value="0">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>
                    </Grid>
                </Grid>
            </GroupBox>

            <!-- 操作按钮区域 -->
            <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
                <Button Content="保存" 
                        Style="{StaticResource MaterialDesignRaisedButton}" 
                        Command="{Binding SaveBomCommand}"
                        Background="#4CAF50"
                        BorderBrush="#4CAF50"
                        Foreground="White"
                        Width="100"
                        Height="35"
                        Margin="10,0"/>
                <Button Content="取消" 
                        Style="{StaticResource MaterialDesignOutlinedButton}" 
                        Command="{Binding CancelCommand}"
                        BorderBrush="#F44336"
                        Foreground="#F44336"
                        Width="100"
                        Height="35"
                        Margin="10,0"/>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>