<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.AiChat"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.UserControl"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="800">
    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:BoolToAlignmentConverter x:Key="BoolToAlignmentConverter"/>
        <local:BoolToBackgroundConverter x:Key="BoolToBackgroundConverter"/>
        <local:BoolToForegroundConverter x:Key="BoolToForegroundConverter"/>
    </UserControl.Resources>
    
    <!-- 使用DockPanel布局，确保输入框固定在底部 -->
    <DockPanel LastChildFill="True">
        <!-- 输入区域 - 固定在底部 -->
        <Border DockPanel.Dock="Bottom" 
                Background="White"
                BorderThickness="0,1,0,0"
                BorderBrush="#DDDDDD">
            <Grid Margin="10,10,10,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 输入框 -->
                <TextBox Grid.Column="0"
                         x:Name="MessageTextBox"
                         Text="{Binding CurrentMessage, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="请输入您的问题..."
                         Height="40"
                         FontSize="14"
                         Margin="0,0,10,0"
                         VerticalContentAlignment="Center"
                         KeyDown="MessageTextBox_KeyDown"/>
                    
                <!-- 发送按钮 -->
                <Button Grid.Column="1"
                        Command="{Binding SendMessageCommand}"
                        Content="发送"
                        Height="40"
                        Width="80"
                        IsEnabled="{Binding CanSendMessage}"/>
            </Grid>
            </Border>

        <!-- 聊天区域 - 填充剩余空间 -->
                <ScrollViewer x:Name="ChatScrollViewer"
                              VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="Transparent">
            <StackPanel Margin="0,0,0,0">
                <ItemsControl x:Name="ChatItemsControl" 
                              ItemsSource="{Binding ChatMessages}">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                            <Grid Margin="10,5">
                                <Border Padding="15,10"
                                        Background="{Binding IsUser, Converter={StaticResource BoolToBackgroundConverter}}"
                                        HorizontalAlignment="{Binding IsUser, Converter={StaticResource BoolToAlignmentConverter}}"
                                        BorderThickness="0"
                                        CornerRadius="0"
                                        MaxWidth="600">
                                    <StackPanel>
                                        <TextBlock Text="{Binding Content}"
                                                   TextWrapping="Wrap"
                                                   FontSize="14"
                                                   Foreground="{Binding IsUser, Converter={StaticResource BoolToForegroundConverter}}"/>
                                        
                                        <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:HH:mm}'}"
                                                   FontSize="10"
                                                   Margin="0,5,0,0"
                                                   HorizontalAlignment="Right"
                                                   Foreground="#999999"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                <!-- 正在输入指示器 -->
                <Border Visibility="{Binding IsAiTyping, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="10,5,10,10"
                        Padding="15,10"
                        HorizontalAlignment="Left"
                        Background="#F5F5F5">
                    <TextBlock Text="AI正在思考中..." 
                             FontSize="14"
                               Foreground="#666666"/>
            </Border>
                </StackPanel>
        </ScrollViewer>
    </DockPanel>
</UserControl>