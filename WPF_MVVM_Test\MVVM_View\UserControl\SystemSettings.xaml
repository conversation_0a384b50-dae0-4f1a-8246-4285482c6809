<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.SystemSettings"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border Background="White"
            CornerRadius="5"
            Padding="20"
            Margin="0,0,0,20">
        <StackPanel>
            <!-- 页面标题 -->
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <TextBlock Text="⚙️ 系统设置"
                           FontSize="20"
                           FontWeight="Bold"
                           VerticalAlignment="Center"/>
                <TextBlock Text="配置系统参数和选项"
                           FontSize="14"
                           Foreground="Gray"
                           VerticalAlignment="Center"
                           Margin="15,0,0,0"/>
            </StackPanel>

            <!-- 设置选项 -->
            <Border Background="#F8F9FA"
                    CornerRadius="5"
                    Padding="20"
                    Margin="0,0,0,20">
                <StackPanel>
                    <TextBlock Text="🔧 基本设置"
                               FontSize="16"
                               FontWeight="Bold"
                               Margin="0,0,0,15"/>
                    
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="150"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- 系统名称 -->
                        <TextBlock Grid.Row="0" Grid.Column="0"
                                   Text="系统名称："
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="0" Grid.Column="1"
                                 Text="MVVM管理系统"
                                 Margin="0,0,0,10"
                                 Padding="8"/>

                        <!-- 系统版本 -->
                        <TextBlock Grid.Row="1" Grid.Column="0"
                                   Text="系统版本："
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,10"/>
                        <TextBox Grid.Row="1" Grid.Column="1"
                                 Text="v1.0.0"
                                 Margin="0,0,0,10"
                                 Padding="8"/>

                        <!-- 自动保存 -->
                        <TextBlock Grid.Row="2" Grid.Column="0"
                                   Text="自动保存："
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,10"/>
                        <CheckBox Grid.Row="2" Grid.Column="1"
                                  Content="启用自动保存功能"
                                  IsChecked="True"
                                  Margin="0,0,0,10"/>

                        <!-- 主题设置 -->
                        <TextBlock Grid.Row="3" Grid.Column="0"
                                   Text="主题："
                                   VerticalAlignment="Center"
                                   Margin="0,0,10,10"/>
                        <ComboBox Grid.Row="3" Grid.Column="1"
                                  Margin="0,0,0,10"
                                  Padding="8">
                            <ComboBoxItem Content="浅色主题" IsSelected="True"/>
                            <ComboBoxItem Content="深色主题"/>
                            <ComboBoxItem Content="自动"/>
                        </ComboBox>
                    </Grid>
                </StackPanel>
            </Border>

            <!-- 操作按钮 -->
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Right">
                <Button Content="💾 保存设置"
                        Background="#4CAF50"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="15,8"
                        Margin="0,0,10,0"
                        Cursor="Hand"/>
                <Button Content="🔄 重置默认"
                        Background="#FF9800"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="15,8"
                        Cursor="Hand"/>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl>
