using System.Windows;
using WPF_MVVM_Test.MVVM_ViewModel.Material;

namespace WPF_MVVM_Test.MVVM_View.Material
{
    /// <summary>
    /// MaterialAddDialog.xaml 的交互逻辑
    /// </summary>
    public partial class MaterialAddDialog : Window
    {
        public MaterialAddDialog()
        {
            InitializeComponent();
            DataContext = new MaterialAddViewModel();
            
            // 订阅ViewModel的关闭事件
            if (DataContext is MaterialAddViewModel viewModel)
            {
                viewModel.RequestClose += (result) =>
                {
                    DialogResult = result;
                    Close();
                };
            }
        }
    }
}