# 工单任务API对接说明

## 概述
本文档说明了工单任务页面与真实API接口的对接实现。

## 已实现的功能

### 1. API模型类
- **WorkOrderTaskApiModel.cs**: 对应后端API返回的数据结构
- **WorkReportRequest.cs**: 报工请求数据模型

### 2. API服务类
- **WorkOrderTaskApiService.cs**: 处理与后端API的通信
  - `GetWorkOrderTaskPageAsync()`: 获取工单任务分页列表
  - `SubmitWorkReportAsync()`: 单个报工
  - `SubmitBatchWorkReportAsync()`: 批量报工

### 3. 报工对话框
- **WorkReportDialog.xaml**: 报工界面
- **WorkReportDialogViewModel.cs**: 报工对话框的ViewModel

### 4. 更新的服务类
- **WorkOrderTaskService.cs**: 更新为使用真实API
- **WorkOrderTaskViewModel.cs**: 更新为支持API调用和报工功能

## API接口说明

### 1. 获取工单任务列表
```
GET http://localhost:64922/api/WorkOrderTask/debug/simple-page?pageIndex=1&pageSize=10
```

**响应数据格式**:
```json
{
  "isSuc": true,
  "code": 200,
  "msg": "成功",
  "data": {
    "totalCount": 100,
    "totalPage": 10,
    "data": [
      {
        "workOrderTaskId": "61b8c909-2c34-2272-7e48-766de087ba35",
        "inspectionName": "质量检验",
        "inspectionType": "首件检验",
        "productId": "8765321-4321-4321-4321-987654321def",
        "processStepId": "11111111-2222-3333-4444-555555555555",
        "stationId": "33333333-4444-5555-6666-777777777777",
        "reportId": "44444444-5555-6666-7777-888888888888",
        "inspectorId": "55555555-6666-7777-8888-999999999999",
        "reportQuantity": 10,
        "reportTime": "2024-01-31T14:30:00",
        "inspectionTime": "2024-01-31T15:00:00",
        "inspectionDepartment": "质检部",
        "testQuantity": 10,
        "qualifiedQuantity": 9,
        "unqualifiedQuantity": 1,
        "overallResult": "合格",
        "remark": "首次检验，整体质量良好，发现轻微瑕疵已记录"
      }
    ]
  }
}
```

### 2. 报工接口
```
POST http://localhost:64922/api/WorkOrderTask/batch-work-report
```

**请求数据格式**:
```json
{
  "workOrderTaskId": "61b8c909-2c34-2272-7e48-766de087ba35",
  "inspectionName": "质量检验",
  "inspectionType": "首件检验",
  "productId": "8765321-4321-4321-4321-987654321def",
  "processStepId": "11111111-2222-3333-4444-555555555555",
  "stationId": "33333333-4444-5555-6666-777777777777",
  "reportId": "44444444-5555-6666-7777-888888888888",
  "inspectorId": "55555555-6666-7777-8888-999999999999",
  "reportQuantity": 10,
  "reportTime": "2024-01-31T14:30:00",
  "inspectionTime": "2024-01-31T15:00:00",
  "inspectionDepartment": "质检部",
  "testQuantity": 10,
  "qualifiedQuantity": 9,
  "unqualifiedQuantity": 1,
  "overallResult": "合格",
  "remark": "检验完成"
}
```

**响应数据格式**:
```json
{
  "isSuc": true,
  "code": 200,
  "msg": "操作成功"
}
```

## 使用说明

### 1. 查看工单任务列表
- 应用程序启动后会自动调用API获取工单任务列表
- 支持分页显示，每页10条记录
- 支持本地搜索和筛选功能

### 2. 报工操作
- 点击任务行的"报工"按钮
- 在弹出的报工对话框中填写相关信息：
  - 检验项名称
  - 检验类型
  - 检验部门
  - 报告数量、测试数量、合格数量、不合格数量
  - 总体结果
  - 备注
- 点击"确定"提交报工数据到后端API

### 3. 批量报工
- 支持通过API进行批量报工
- 可以传递多个报工记录的数组

## 技术实现要点

### 1. 数据转换
- API返回的数据通过`ConvertToUIModel`方法转换为UI模型
- 根据`overallResult`字段映射任务状态

### 2. 错误处理
- API调用失败时显示错误信息
- 网络异常时提供友好的错误提示

### 3. 异步操作
- 所有API调用都是异步的，不会阻塞UI线程
- 加载过程中显示加载状态

## 注意事项

1. 确保后端API服务正在运行（http://localhost:64922）
2. API接口需要支持CORS跨域请求
3. 报工时的一些字段（如productId、processStepId等）目前使用默认值，实际使用时需要从任务数据中获取
4. 当前实现的搜索功能是在客户端进行的，如果数据量大建议在服务端实现搜索功能

## 后续优化建议

1. 添加API认证和授权机制
2. 实现服务端搜索和筛选
3. 添加数据缓存机制
4. 完善错误处理和重试机制
5. 添加数据验证
6. 实现实时数据更新（WebSocket或SignalR）
