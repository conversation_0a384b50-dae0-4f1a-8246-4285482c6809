<Window x:Class="WPF_MVVM_Test.MVVM_View.Dialog.SelectBomDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择BOM" Height="650" Width="1000"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="35"/>  <!-- 标题 -->
            <RowDefinition Height="50"/>  <!-- 查询条件 -->
            <RowDefinition Height="40"/>  <!-- 产品信息 -->
            <RowDefinition Height="*"/>   <!-- BOM列表 -->
            <RowDefinition Height="50"/>  <!-- 分页控件 -->
            <RowDefinition Height="45"/>  <!-- 按钮 -->
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,0">
            <TextBlock Text="选择BOM" FontSize="16" FontWeight="Bold" VerticalAlignment="Center"/>
            <Button Content="✕" HorizontalAlignment="Right" Width="25" Height="25" 
                    Background="Transparent" BorderThickness="0" FontSize="14"
                    Click="CloseButton_Click" Margin="920,0,0,0"/>
        </StackPanel>

        <!-- 查询条件 -->
        <Border Grid.Row="1" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="1" 
                CornerRadius="3" Padding="8" Margin="0,0,0,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="产品名称：" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                <TextBox Width="150" Text="{Binding ProductNameFilter, UpdateSourceTrigger=PropertyChanged}" 
                         Margin="0,0,15,0" Height="22" FontSize="12"/>
                <TextBlock Text="BOM编号：" VerticalAlignment="Center" Margin="0,0,5,0" FontSize="12"/>
                <TextBox Width="150" Text="{Binding BomNumberFilter, UpdateSourceTrigger=PropertyChanged}" 
                         Margin="0,0,15,0" Height="22" FontSize="12"/>
                <Button Content="查询" Width="60" Height="22" Command="{Binding SearchCommand}" 
                        Background="#007BFF" Foreground="White" BorderThickness="0" FontSize="12"/>
            </StackPanel>
        </Border>

        <!-- 产品信息 -->
        <Border Grid.Row="2" Background="#F8F9FA" BorderBrush="#E9ECEF" BorderThickness="1" 
                CornerRadius="3" Padding="8" Margin="0,0,0,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="产品名称：" FontWeight="Bold" FontSize="12" Margin="0,0,5,0"/>
                <TextBlock Text="{Binding ProductInfo.ProductName}" FontSize="12" Margin="0,0,25,0"/>
                <TextBlock Text="产品编号：" FontWeight="Bold" FontSize="12" Margin="0,0,5,0"/>
                <TextBlock Text="{Binding ProductInfo.ProductNumber}" FontSize="12" Margin="0,0,25,0"/>
                <TextBlock Text="规格型号：" FontWeight="Bold" FontSize="12" Margin="0,0,5,0"/>
                <TextBlock Text="{Binding ProductInfo.Specification}" FontSize="12" Margin="0,0,25,0"/>
                <TextBlock Text="单位：" FontWeight="Bold" FontSize="12" Margin="0,0,5,0"/>
                <TextBlock Text="{Binding ProductInfo.Unit}" FontSize="12"/>
            </StackPanel>
        </Border>

        <!-- BOM列表 -->
        <DataGrid Grid.Row="3" ItemsSource="{Binding BomList}" 
                  SelectedItem="{Binding SelectedBom}"
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  Background="White"
                  Margin="0,0,0,5"
                  RowHeight="30"
                  FontSize="12">
            <DataGrid.Columns>
                <DataGridTemplateColumn Header="" Width="35">
                    <DataGridTemplateColumn.CellTemplate>
                        <DataTemplate>
                            <RadioButton GroupName="BomSelection" 
                                         IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                         HorizontalAlignment="Center"/>
                        </DataTemplate>
                    </DataGridTemplateColumn.CellTemplate>
                </DataGridTemplateColumn>
                <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60"/>
                <DataGridTextColumn Header="BOM编号" Binding="{Binding BomCode}" Width="180"/>
                <DataGridTextColumn Header="版本号" Binding="{Binding Version}" Width="100"/>
                <DataGridTextColumn Header="默认BOM" Binding="{Binding IsDefaultBom}" Width="100"/>
                <DataGridTextColumn Header="日产量" Binding="{Binding Quantity}" Width="100"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页控件 -->
        <Border Grid.Row="4" Background="#F5F5F5" BorderBrush="#E0E0E0" BorderThickness="1" 
                CornerRadius="3" Padding="8" Margin="0,0,0,5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="{Binding PageInfo}" VerticalAlignment="Center" Margin="0,0,20,0" FontSize="12"/>
                <Button Content="上一页" Command="{Binding PreviousPageCommand}" IsEnabled="{Binding CanGoPreviousPage}" 
                        Width="60" Height="25" Margin="2" Background="#6C757D" Foreground="White" BorderThickness="0" FontSize="12"/>
                <TextBlock Text="第" VerticalAlignment="Center" Margin="8,0,0,0" FontSize="12"/>
                <TextBlock Text="{Binding CurrentPage}" VerticalAlignment="Center" Margin="2,0" FontSize="12"/>
                <TextBlock Text="/" VerticalAlignment="Center" FontSize="12"/>
                <TextBlock Text="{Binding TotalPage}" VerticalAlignment="Center" Margin="2,0" FontSize="12"/>
                <TextBlock Text="页" VerticalAlignment="Center" Margin="0,0,8,0" FontSize="12"/>
                <Button Content="下一页" Command="{Binding NextPageCommand}" IsEnabled="{Binding CanGoNextPage}" 
                        Width="60" Height="25" Margin="2" Background="#6C757D" Foreground="White" BorderThickness="0" FontSize="12"/>
                <TextBlock Text="前往" VerticalAlignment="Center" Margin="15,0,5,0" FontSize="12"/>
                <TextBox Width="40" Text="{Binding JumpPage, UpdateSourceTrigger=PropertyChanged}" 
                         Margin="0,0,5,0" Height="22" FontSize="12"/>
                <Button Content="Go" Command="{Binding GoToPageCommand}" CommandParameter="{Binding JumpPage}" 
                        Width="70" Height="22" Background="#28A745" Foreground="White" BorderThickness="0" FontSize="12"/>
                <TextBlock Text="页" VerticalAlignment="Center" FontSize="12"/>
        </StackPanel>
        </Border>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,0">
            <Button Content="取消" Command="{Binding CancelCommand}" 
                    Width="80" Height="32" Margin="0,0,10,0" 
                    Background="#6C757D" Foreground="White" BorderThickness="0" FontSize="12"/>
            <Button Content="确定" Command="{Binding ConfirmCommand}" 
                    Width="80" Height="32" Margin="0,0,0,0" 
                    Background="#007BFF" Foreground="White" BorderThickness="0" FontSize="12"/>
        </StackPanel>
    </Grid>
</Window>