<Window x:Class="WPF_MVVM_Test.MVVM_View.Dialog.SelectProductDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="选择产品" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="选择产品" FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>

        <!-- 搜索区域 -->
        <Border Grid.Row="1" Background="#F5F5F5" CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="产品编号：" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBox Text="{Binding SearchMaterialNumber, UpdateSourceTrigger=PropertyChanged}" 
                         Width="200" Height="30" Margin="0,0,20,0"/>
                
                <Button Content="搜索" Command="{Binding SearchCommand}" 
                        Width="80" Height="30" Margin="0,0,10,0"/>
                <Button Content="重置" Command="{Binding ResetCommand}" 
                        Width="80" Height="30"/>
            </StackPanel>
        </Border>

        <!-- 产品列表 -->
        <DataGrid Grid.Row="2" ItemsSource="{Binding Products}" 
                  SelectedItem="{Binding SelectedProduct}"
                  AutoGenerateColumns="False" 
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single">
            <DataGrid.Columns>
                <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60"/>
                <DataGridTextColumn Header="产品编号" Binding="{Binding MaterialNumber}" Width="120"/>
                <DataGridTextColumn Header="产品名称" Binding="{Binding MaterialName}" Width="150"/>
                <DataGridTextColumn Header="规格型号" Binding="{Binding SpecificationModel}" Width="120"/>
                <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="80"/>
                <DataGridTextColumn Header="产品类型" Binding="{Binding MaterialType}" Width="100"/>
                <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="80"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- 分页信息 -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,15,0,0">
            <Button Content="上一页" Command="{Binding PreviousPageCommand}" 
                    IsEnabled="{Binding CanGoPreviousPage}" Width="80" Height="30" Margin="0,0,10,0"/>
            <TextBlock Text="{Binding PageInfo}" VerticalAlignment="Center" Margin="10,0"/>
            <Button Content="下一页" Command="{Binding NextPageCommand}" 
                    IsEnabled="{Binding CanGoNextPage}" Width="80" Height="30" Margin="10,0,0,0"/>
        </StackPanel>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button Content="确定" Command="{Binding ConfirmCommand}" 
                    Width="80" Height="35" Margin="0,0,10,0" 
                    IsDefault="True"/>
            <Button Content="取消" IsCancel="True" 
                    Width="80" Height="35"/>
        </StackPanel>
    </Grid>
</Window>

