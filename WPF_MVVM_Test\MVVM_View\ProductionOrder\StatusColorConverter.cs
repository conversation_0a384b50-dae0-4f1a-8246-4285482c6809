using System;
using System.Globalization;
using System.Windows.Data;
using System.Windows.Media;

namespace WPF_MVVM_Test.MVVM_View.ProductionOrder
{
    /// <summary>
    /// 状态颜色转换器 - 将状态值转换为对应的颜色
    /// </summary>
    public class StatusColorConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is int status)
            {
                return status switch
                {
                    0 => new SolidColorBrush(Color.FromRgb(140, 140, 140)), // 待排产 - 灰色
                    1 => new SolidColorBrush(Color.FromRgb(24, 144, 255)),   // 未开始 - 蓝色
                    2 => new SolidColorBrush(Color.FromRgb(82, 196, 26)),    // 进行中 - 绿色
                    3 => new SolidColorBrush(Color.FromRgb(250, 140, 22)),   // 已完成 - 橙色
                    4 => new SolidColorBrush(Color.FromRgb(255, 77, 79)),    // 已暂停 - 红色
                    5 => new SolidColorBrush(Color.FromRgb(108, 117, 125)),  // 已关闭 - 深灰色
                    _ => new SolidColorBrush(Color.FromRgb(140, 140, 140))   // 默认灰色
                };
            }
            
            return new SolidColorBrush(Color.FromRgb(140, 140, 140)); // 默认灰色
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
} 