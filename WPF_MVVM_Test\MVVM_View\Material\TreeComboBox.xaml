<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Material.TreeComboBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Material"
             mc:Ignorable="d" 
             d:DesignHeight="60" d:DesignWidth="300">

    <UserControl.Resources>
        <!-- 层级到边距转换器 -->
        <local:LevelToMarginConverter x:Key="LevelToMarginConverter"/>
        <local:LevelToVisibilityConverter x:Key="LevelToVisibilityConverter"/>
    </UserControl.Resources>

    <Grid>
        <ComboBox x:Name="CategoryComboBox"
                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                  materialDesign:HintAssist.Hint="{Binding HintText, RelativeSource={RelativeSource AncestorType=UserControl}}"
                  Height="60"
                  IsEditable="False"
                  DropDownOpened="CategoryComboBox_DropDownOpened">
            
            <ComboBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal" 
                                Margin="{Binding Level, Converter={StaticResource LevelToMarginConverter}}">
                        <!-- 层级缩进指示器 -->
                        <Rectangle Width="2" 
                                   Height="16" 
                                   Fill="#CCCCCC" 
                                   Margin="0,0,5,0"
                                   Visibility="{Binding Level, Converter={StaticResource LevelToVisibilityConverter}}"/>
                        
                        <!-- 分类名称 -->
                        <TextBlock Text="{Binding CategoryName}" 
                                   VerticalAlignment="Center"
                                   FontSize="13"/>
                    </StackPanel>
                </DataTemplate>
            </ComboBox.ItemTemplate>
        </ComboBox>
    </Grid>
</UserControl>