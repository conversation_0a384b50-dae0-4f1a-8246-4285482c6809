﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using WPF_MVVM_Test.MVVM_ViewModel;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;

namespace WPF_MVVM_Test.MVVM_View.Productplan
{
    /// <summary>
    /// ProductplanList.xaml 的交互逻辑
    /// </summary>
    public partial class ProductplanList : System.Windows.Controls.UserControl
    {
        public ProductplanList()
        {
            InitializeComponent();
            // 设置DataContext为ProductPlanViewModel
            DataContext = new ProductPlanViewModel();
        }
    }
}
