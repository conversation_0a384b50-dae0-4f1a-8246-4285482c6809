using System;
using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.Bom
{
    /// <summary>
    /// 产品信息模型
    /// </summary>
    public class ProductEntity
    {
        public string Id { get; set; } = "";
        public string MaterialNumber { get; set; } = "";
        public string MaterialName { get; set; } = "";
        public string SpecificationModel { get; set; } = "";
        public string Unit { get; set; } = "";
        public string? MaterialType { get; set; }
        public string MaterialProperty { get; set; } = "";
        public string MaterialCategoryId { get; set; } = "";
        public string Status { get; set; } = "";
        public DateTime EffectiveDate { get; set; }
        public string EffectiveUnit { get; set; } = "";
        public int AlarmDays { get; set; }
        public decimal StockUpperLimit { get; set; }
        public decimal StockLowerLimit { get; set; }
        public decimal PurchasePrice { get; set; }
        public decimal SalesPrice { get; set; }
        public string Remarks { get; set; } = "";
        public string MaterialImage { get; set; } = "";
        public string Attachment { get; set; } = "";
        public int Index { get; set; } = 1;
    }

    /// <summary>
    /// 产品分页数据
    /// </summary>
    public class ProductPagedData
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<ProductEntity> Data { get; set; } = new List<ProductEntity>();
    }

    /// <summary>
    /// 产品查询参数
    /// </summary>
    public class ProductQueryParams
    {
        public string MaterialNumber { get; set; } = "";
        public string SpecificationModel { get; set; } = "";
        public string Unit { get; set; } = "";
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 12;
    }

    /// <summary>
    /// 产品API响应模型
    /// </summary>
    public class ProductApiResponses
    {
        public ProductPagedData Data { get; set; } = new();
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; } = "";
    }
}