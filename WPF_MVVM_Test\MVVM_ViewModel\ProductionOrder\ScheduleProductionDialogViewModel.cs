using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.ProductionOrder;
using WPF_MVVM_Test.MVVM_Services;

namespace WPF_MVVM_Test.MVVM_ViewModel.ProductionOrder
{
    /// <summary>
    /// 物料清单项
    /// </summary>
    public class BomItem
    {
        public string MaterialName { get; set; } = string.Empty;
        public string MaterialNumber { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public decimal RequiredQuantity { get; set; }
        public string Unit { get; set; } = string.Empty;
        public decimal AvailableQuantity { get; set; }
        public string Status { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工艺流程项
    /// </summary>
    public class ProcessItem
    {
        public int Sequence { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public string WorkCenter { get; set; } = string.Empty;
        public decimal StandardTime { get; set; }
        public string TimeUnit { get; set; } = "小时";
        public string Status { get; set; } = "待开始";
        public string Description { get; set; } = string.Empty;
        public bool IsSelected { get; set; }
        public ObservableCollection<ProcessTaskItem> Tasks { get; set; } = new ObservableCollection<ProcessTaskItem>();
    }

    /// <summary>
    /// 工序任务项
    /// </summary>
    public class ProcessTaskItem
    {
        public int Index { get; set; }
        public string TaskCode { get; set; } = string.Empty;
        public string TaskName { get; set; } = string.Empty;
        public string StationName { get; set; } = string.Empty;
        public string StationCode { get; set; } = string.Empty;
        public int PlanQuantity { get; set; }
        public DateTime PlanStartTime { get; set; }
        public DateTime PlanEndTime { get; set; }
        public string TaskStatus { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工艺路线信息
    /// </summary>
    public class ProcessRouteInfo
    {
        public string RouteName { get; set; } = string.Empty;
        public string RouteCode { get; set; } = string.Empty;
    }

    /// <summary>
    /// 排产对话框ViewModel
    /// </summary>
    public class ScheduleProductionDialogViewModel : BaseViewModel
    {
        #region 私有字段
        private readonly ProductionOrderModel _originalOrder;
        private readonly ProductionOrderService _productionOrderService;

        // 基础信息字段（只读）
        private string _orderNumber = string.Empty;
        private string _productName = string.Empty;
        private string _productNumber = string.Empty;
        private string _specification = string.Empty;
        private string _unit = string.Empty;
        private string _bomCode = string.Empty;

        // 可编辑字段
        private decimal _planQuantity = 0;
        private DateTime? _startTime = DateTime.Today;
        private DateTime? _endTime = DateTime.Today.AddDays(7);
        private string _remark = string.Empty;

        // 对话框结果
        private bool _dialogResult = false;
        #endregion

        #region 构造函数
        public ScheduleProductionDialogViewModel(ProductionOrderModel order)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 开始初始化ScheduleProductionDialogViewModel...");

                // 1. 验证参数
                if (order == null)
                {
                    System.Diagnostics.Debug.WriteLine($"💥 order参数为null");
                    throw new ArgumentNullException(nameof(order));
                }
                _originalOrder = order;
                System.Diagnostics.Debug.WriteLine($"✅ 原始订单数据已设置: {order.OrderNumber ?? "未知编号"}");

                // 2. 创建服务（可能会失败）
                try
                {
                    _productionOrderService = new ProductionOrderService();
                    System.Diagnostics.Debug.WriteLine($"✅ ProductionOrderService已创建");
                }
                catch (Exception serviceEx)
                {
                    System.Diagnostics.Debug.WriteLine($"💥 创建ProductionOrderService失败: {serviceEx.Message}");
                    throw new Exception($"创建生产工单服务失败: {serviceEx.Message}", serviceEx);
                }

                // 3. 初始化命令（可能会失败）
                try
                {
                    ConfirmCommand = CreateAsyncCommand(ConfirmAsync);
                    CancelCommand = CreateCommand(Cancel);
                    SelectProcessCommand = CreateCommand<ProcessItem>(SelectProcess);
                    System.Diagnostics.Debug.WriteLine($"✅ 命令已初始化");
                }
                catch (Exception cmdEx)
                {
                    System.Diagnostics.Debug.WriteLine($"💥 初始化命令失败: {cmdEx.Message}");
                    throw new Exception($"初始化命令失败: {cmdEx.Message}", cmdEx);
                }

                // 4. 反填数据（可能会失败）
                try
                {
                    InitializeData();
                    System.Diagnostics.Debug.WriteLine($"✅ 数据初始化完成");
                }
                catch (Exception dataEx)
                {
                    System.Diagnostics.Debug.WriteLine($"💥 数据初始化失败: {dataEx.Message}");
                    throw new Exception($"数据初始化失败: {dataEx.Message}", dataEx);
                }

                System.Diagnostics.Debug.WriteLine($"✅ ScheduleProductionDialogViewModel初始化成功");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"💥 ScheduleProductionDialogViewModel初始化失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"💥 堆栈跟踪: {ex.StackTrace}");
                throw;
            }
        }
        #endregion

        #region 公共属性 - 基础信息（只读）
        /// <summary>
        /// 工单编号
        /// </summary>
        public string OrderNumber
        {
            get => _orderNumber;
            private set => SetProperty(ref _orderNumber, value);
        }

        /// <summary>
        /// 产品名称
        /// </summary>
        public string ProductName
        {
            get => _productName;
            private set => SetProperty(ref _productName, value);
        }

        /// <summary>
        /// 产品编号
        /// </summary>
        public string ProductNumber
        {
            get => _productNumber;
            private set => SetProperty(ref _productNumber, value);
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string Specification
        {
            get => _specification;
            private set => SetProperty(ref _specification, value);
        }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit
        {
            get => _unit;
            private set => SetProperty(ref _unit, value);
        }

        /// <summary>
        /// BOM编码
        /// </summary>
        public string BomCode
        {
            get => _bomCode;
            private set => SetProperty(ref _bomCode, value);
        }

        /// <summary>
        /// 物料清单
        /// </summary>
        public ObservableCollection<BomItem> BomItems { get; private set; } = new ObservableCollection<BomItem>();

        /// <summary>
        /// 工艺流程
        /// </summary>
        public ObservableCollection<ProcessItem> ProcessItems { get; private set; } = new ObservableCollection<ProcessItem>();

        /// <summary>
        /// 工艺路线信息
        /// </summary>
        public ProcessRouteInfo ProcessRoute { get; private set; } = new ProcessRouteInfo();

        /// <summary>
        /// 当前选中的工序
        /// </summary>
        private ProcessItem? _selectedProcess;
        public ProcessItem? SelectedProcess
        {
            get => _selectedProcess;
            set => SetProperty(ref _selectedProcess, value);
        }
        #endregion

        #region 公共属性 - 可编辑字段
        /// <summary>
        /// 计划数量
        /// </summary>
        public decimal PlanQuantity
        {
            get => _planQuantity;
            set => SetProperty(ref _planQuantity, value);
        }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        public DateTime? StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        public DateTime? EndTime
        {
            get => _endTime;
            set => SetProperty(ref _endTime, value);
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark
        {
            get => _remark;
            set => SetProperty(ref _remark, value);
        }
        #endregion

        #region 命令
        public ICommand ConfirmCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand SelectProcessCommand { get; }
        #endregion

        #region 公共属性
        /// <summary>
        /// 对话框结果
        /// </summary>
        public bool DialogResult
        {
            get => _dialogResult;
            private set => SetProperty(ref _dialogResult, value);
        }
        #endregion

        #region 私有方法
        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 反填基础信息（只读）
            OrderNumber = _originalOrder.OrderNumber ?? string.Empty;
            ProductName = _originalOrder.ProductName ?? string.Empty;
            ProductNumber = _originalOrder.ProductNumber ?? string.Empty;
            Specification = _originalOrder.Specification ?? string.Empty;
            Unit = _originalOrder.Unit ?? string.Empty;
            BomCode = "BOM001"; // 这里可以根据实际情况从订单中获取BOM信息

            // 反填可编辑字段
            PlanQuantity = _originalOrder.PlanQuantity;
            StartTime = _originalOrder.PlanStartTime ?? DateTime.Today;
            EndTime = _originalOrder.PlanEndTime ?? DateTime.Today.AddDays(7);
            Remark = string.Empty; // 排产备注通常是新的

            // 初始化物料清单（示例数据）
            InitializeBomItems();

            // 初始化工艺流程（示例数据）
            InitializeProcessItems();
        }

        /// <summary>
        /// 初始化物料清单
        /// </summary>
        private void InitializeBomItems()
        {
            BomItems.Clear();

            // 添加示例物料数据
            BomItems.Add(new BomItem
            {
                MaterialName = "钢板",
                MaterialNumber = "M001",
                Specification = "Q235 10mm",
                RequiredQuantity = PlanQuantity * 2.5m,
                Unit = "kg",
                AvailableQuantity = 1000,
                Status = "充足"
            });

            BomItems.Add(new BomItem
            {
                MaterialName = "螺栓",
                MaterialNumber = "M002",
                Specification = "M8×20",
                RequiredQuantity = PlanQuantity * 4,
                Unit = "个",
                AvailableQuantity = 500,
                Status = "充足"
            });

            BomItems.Add(new BomItem
            {
                MaterialName = "焊条",
                MaterialNumber = "M003",
                Specification = "E4303 φ3.2",
                RequiredQuantity = PlanQuantity * 0.5m,
                Unit = "kg",
                AvailableQuantity = 20,
                Status = "紧张"
            });
        }

        /// <summary>
        /// 初始化工艺流程
        /// </summary>
        private void InitializeProcessItems()
        {
            ProcessItems.Clear();

            // 设置工艺路线信息
            ProcessRoute.RouteName = "新产品工艺路线";
            ProcessRoute.RouteCode = "GYLX05404";

            // 工序1：下料
            var process1 = new ProcessItem
            {
                Sequence = 1,
                ProcessName = "工序一",
                WorkCenter = "切割车间",
                StandardTime = 2.0m,
                TimeUnit = "小时",
                Status = "待开始",
                Description = "下料工序",
                IsSelected = true
            };

            // 下料工序的具体任务
            process1.Tasks.Add(new ProcessTaskItem
            {
                Index = 1,
                TaskCode = "RWBH1",
                TaskName = "任务一",
                StationName = "站点一",
                StationCode = "zd0215",
                PlanQuantity = 50,
                PlanStartTime = DateTime.Parse("2025-07-31 22:14"),
                PlanEndTime = DateTime.Parse("2025-07-31 22:14"),
                TaskStatus = ""
            });

            process1.Tasks.Add(new ProcessTaskItem
            {
                Index = 2,
                TaskCode = "RWBH2",
                TaskName = "任务一",
                StationName = "站点一",
                StationCode = "zd0215",
                PlanQuantity = 50,
                PlanStartTime = DateTime.Parse("2025-07-31 22:14"),
                PlanEndTime = DateTime.Parse("2025-07-31 22:14"),
                TaskStatus = ""
            });

            process1.Tasks.Add(new ProcessTaskItem
            {
                Index = 3,
                TaskCode = "RWBH3",
                TaskName = "任务一",
                StationName = "站点一",
                StationCode = "zd0215",
                PlanQuantity = 50,
                PlanStartTime = DateTime.Parse("2025-07-31 22:14"),
                PlanEndTime = DateTime.Parse("2025-07-31 22:14"),
                TaskStatus = ""
            });

            ProcessItems.Add(process1);

            // 工序2：机加工
            var process2 = new ProcessItem
            {
                Sequence = 2,
                ProcessName = "工序二",
                WorkCenter = "机加车间",
                StandardTime = 4.0m,
                TimeUnit = "小时",
                Status = "待开始",
                Description = "机加工工序"
            };

            ProcessItems.Add(process2);

            // 工序3：焊接
            var process3 = new ProcessItem
            {
                Sequence = 3,
                ProcessName = "工序三",
                WorkCenter = "焊接车间",
                StandardTime = 3.0m,
                TimeUnit = "小时",
                Status = "待开始",
                Description = "焊接工序"
            };

            ProcessItems.Add(process3);

            // 工序4：检验
            var process4 = new ProcessItem
            {
                Sequence = 4,
                ProcessName = "工序四",
                WorkCenter = "质检部",
                StandardTime = 1.0m,
                TimeUnit = "小时",
                Status = "待开始",
                Description = "质量检验"
            };

            ProcessItems.Add(process4);

            // 默认选中第一个工序
            SelectedProcess = ProcessItems.FirstOrDefault();
        }

        /// <summary>
        /// 确定命令
        /// </summary>
        private async Task ConfirmAsync()
        {
            try
            {
                // 验证数据
                if (!ValidateData())
                    return;

                // 调用排产API
                var success = await ScheduleProductionAsync();

                if (success)
                {
                    DialogResult = true;
                    System.Windows.MessageBox.Show("排产成功！", "提示",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);

                    // 关闭对话框
                    CloseDialog();
                }
                else
                {
                    System.Windows.MessageBox.Show("排产失败，请重试！", "错误",
                        System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                System.Windows.MessageBox.Show($"排产失败：{ex.Message}", "错误",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消命令
        /// </summary>
        private void Cancel()
        {
            DialogResult = false;
            CloseDialog();
        }

        /// <summary>
        /// 验证数据
        /// </summary>
        private bool ValidateData()
        {
            if (PlanQuantity <= 0)
            {
                System.Windows.MessageBox.Show("计划数量必须大于0！", "验证失败",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            if (!StartTime.HasValue)
            {
                System.Windows.MessageBox.Show("请选择计划开工时间！", "验证失败",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            if (EndTime.HasValue && StartTime.HasValue && EndTime.Value < StartTime.Value)
            {
                System.Windows.MessageBox.Show("完工时间不能早于开工时间！", "验证失败",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// 执行排产操作
        /// </summary>
        private async Task<bool> ScheduleProductionAsync()
        {
            try
            {
                // 创建排产请求模型
                var scheduleRequest = new ScheduleProductionRequest
                {
                    OrderId = _originalOrder.Id,
                    PlanQuantity = PlanQuantity,
                    PlanStartTime = StartTime,
                    PlanEndTime = EndTime,
                    Remark = Remark
                };

                // 调用服务进行排产
                var response = await _productionOrderService.ScheduleProductionAsync(scheduleRequest);
                return response.IsSuc;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"排产操作异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 关闭对话框
        /// </summary>
        private void CloseDialog()
        {
            // 查找当前对话框窗口并关闭
            var currentWindow = System.Windows.Application.Current.Windows
                .OfType<System.Windows.Window>()
                .FirstOrDefault(w => w.DataContext == this);

            if (currentWindow != null)
            {
                currentWindow.DialogResult = DialogResult;
                currentWindow.Close();
            }
        }

        /// <summary>
        /// 选择工序
        /// </summary>
        private void SelectProcess(ProcessItem process)
        {
            if (process == null) return;

            // 取消所有工序的选中状态
            foreach (var item in ProcessItems)
            {
                item.IsSelected = false;
            }

            // 设置当前工序为选中状态
            process.IsSelected = true;
            SelectedProcess = process;
        }
        #endregion

        #region IDisposable
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _productionOrderService?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }

    /// <summary>
    /// 排产请求模型
    /// </summary>
    public class ScheduleProductionRequest
    {
        public string OrderId { get; set; } = string.Empty;
        public decimal PlanQuantity { get; set; }
        public DateTime? PlanStartTime { get; set; }
        public DateTime? PlanEndTime { get; set; }
        public string Remark { get; set; } = string.Empty;
    }
}
