using System.Windows;
using WPF_MVVM_Test.MVVM_Model.Plan;
using WPF_MVVM_Test.MVVM_ViewModel;
using WPF_MVVM_Test.MVVM_ViewModel.Plan;

namespace WPF_MVVM_Test.MVVM_View.Dialog
{
    /// <summary>
    /// SelectSalesOrderDialog.xaml 的交互逻辑
    /// </summary>
    public partial class SelectSalesOrderDialog : Window
    {
        private SelectSalesOrderViewModel _viewModel;

        public SalesOrderModel? SelectedSalesOrder { get; private set; }

        public SelectSalesOrderDialog()
        {
            InitializeComponent();
            
            _viewModel = new SelectSalesOrderViewModel();
            DataContext = _viewModel;

            // 订阅选择事件
            _viewModel.OrderSelected += OnOrderSelected;
        }

        private void OnOrderSelected(SalesOrderModel selectedOrder)
        {
            SelectedSalesOrder = selectedOrder;
            DialogResult = true;
            Close();
        }

        protected override void OnClosed(System.EventArgs e)
        {
            // 取消订阅事件
            if (_viewModel != null)
            {
                _viewModel.OrderSelected -= OnOrderSelected;
            }
            base.OnClosed(e);
        }
    }
}