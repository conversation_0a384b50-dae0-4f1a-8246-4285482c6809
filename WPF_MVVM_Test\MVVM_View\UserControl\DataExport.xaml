<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.DataExport"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1000">

    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            
            <!-- 页面标题 -->
            <TextBlock Text="鱼塘数据导出工具" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Margin="0,0,0,20"
                       HorizontalAlignment="Center"/>

            <!-- 统计信息区域 -->
            <GroupBox Header="导出统计" Margin="0,10" Padding="15">
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <StackPanel Margin="20,0">
                        <TextBlock Text="{Binding Statistics.TotalExports}" 
                                   FontSize="18" 
                                   FontWeight="Bold" 
                                   Foreground="Blue"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="总导出次数" 
                                   FontSize="10" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Margin="20,0">
                        <TextBlock Text="{Binding Statistics.SuccessExports}" 
                                   FontSize="18" 
                                   FontWeight="Bold" 
                                   Foreground="Green"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="成功导出" 
                                   FontSize="10" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Margin="20,0">
                        <TextBlock Text="{Binding Statistics.FailedExports}" 
                                   FontSize="18" 
                                   FontWeight="Bold" 
                                   Foreground="Red"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="失败导出" 
                                   FontSize="10" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Margin="20,0">
                        <TextBlock Text="{Binding Statistics.TotalSizeText}" 
                                   FontSize="18" 
                                   FontWeight="Bold" 
                                   Foreground="Purple"
                                   HorizontalAlignment="Center"/>
                        <TextBlock Text="总文件大小" 
                                   FontSize="10" 
                                   HorizontalAlignment="Center"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- 鱼塘数据导出 -->
            <GroupBox Header="导出鱼塘数据" Margin="0,10" Padding="15">
                <StackPanel>
                    <TextBlock Text="导出鱼塘的详细信息，包括名称、类型、状态等。" 
                               FontSize="12" 
                               Foreground="Gray" 
                               Margin="0,0,0,15"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <StackPanel Margin="0,0,20,0">
                            <Label Content="鱼塘类型ID:"/>
                            <TextBox Text="{Binding FishTypeId}" Width="120" Height="25"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,20,0">
                            <Label Content="鱼塘状态:"/>
                            <ComboBox SelectedValue="{Binding FishStatus}" 
                                      DisplayMemberPath="Value"
                                      SelectedValuePath="Key"
                                      ItemsSource="{Binding FishStatusOptions}"
                                      Width="120" Height="25"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,20,0">
                            <Label Content="搜索关键词:"/>
                            <TextBox Text="{Binding FishKeyword}" Width="120" Height="25"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <Button Command="{Binding ExportFishDataCommand}"
                            Content="导出鱼塘数据"
                            Background="LightBlue"
                            Width="150"
                            Height="35"/>
                </StackPanel>
            </GroupBox>

            <!-- 鱼塘类型数据导出 -->
            <GroupBox Header="导出鱼塘类型数据" Margin="0,10" Padding="15">
                <StackPanel>
                    <TextBlock Text="导出所有鱼塘类型的基础信息。" 
                               FontSize="12" 
                               Foreground="Gray" 
                               Margin="0,0,0,15"/>
                    
                    <Button Command="{Binding ExportFishTypeDataCommand}"
                            Content="导出鱼塘类型数据"
                            Background="LightGreen"
                            Width="150"
                            Height="35"/>
                </StackPanel>
            </GroupBox>

            <!-- 鱼塘统计报表导出 -->
            <GroupBox Header="导出鱼塘统计报表" Margin="0,10" Padding="15">
                <StackPanel>
                    <TextBlock Text="导出鱼塘的统计分析报表。" 
                               FontSize="12" 
                               Foreground="Gray" 
                               Margin="0,0,0,15"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <StackPanel Margin="0,0,20,0">
                            <Label Content="开始日期:"/>
                            <DatePicker SelectedDate="{Binding StartDate}" Width="120" Height="25"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,20,0">
                            <Label Content="结束日期:"/>
                            <DatePicker SelectedDate="{Binding EndDate}" Width="120" Height="25"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <Button Command="{Binding ExportStatisticsCommand}"
                            Content="导出统计报表"
                            Background="LightCyan"
                            Width="150"
                            Height="35"/>
                </StackPanel>
            </GroupBox>

            <!-- 异步导出任务测试 -->
            <GroupBox Header="异步导出任务测试" Margin="0,10" Padding="15">
                <StackPanel>
                    <TextBlock Text="测试异步导出功能，适合大数据量导出。" 
                               FontSize="12" 
                               Foreground="Gray" 
                               Margin="0,0,0,15"/>
                    
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                        <StackPanel Margin="0,0,20,0">
                            <Label Content="导出类型:"/>
                            <ComboBox ItemsSource="{Binding ExportTypeOptions}"
                                      SelectedItem="{Binding AsyncExportType}"
                                      Width="120" Height="25"/>
                        </StackPanel>

                        <StackPanel Margin="0,0,20,0">
                            <Label Content="任务优先级:"/>
                            <ComboBox ItemsSource="{Binding PriorityOptions}"
                                      SelectedItem="{Binding TaskPriority}"
                                      Width="120" Height="25"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <Button Command="{Binding SubmitAsyncTaskCommand}"
                            Content="提交异步导出任务"
                            Background="Plum"
                            Width="180"
                            Height="35"/>

                    <!-- 当前任务信息 - 简化版本 -->
                    <Border Background="LightYellow"
                            BorderBrush="Orange"
                            BorderThickness="1"
                            Padding="10"
                            Margin="0,15,0,0"
                            Visibility="{Binding IsCurrentTaskVisible, Converter={StaticResource BooleanToVisibilityConverter}}">
                        <StackPanel>
                            <TextBlock FontWeight="Bold" Margin="0,0,0,5">
                                <Run Text="当前任务: "/>
                                <Run Text="{Binding CurrentTask.TaskId, FallbackValue=无}"/>
                            </TextBlock>
                            <TextBlock Margin="0,0,0,5">
                                <Run Text="状态: " FontWeight="Bold"/>
                                <Run Text="{Binding CurrentTask.Status, FallbackValue=无}"/>
                            </TextBlock>
                            <TextBlock>
                                <Run Text="进度: " FontWeight="Bold"/>
                                <Run Text="{Binding CurrentTask.Progress, FallbackValue=0}"/>
                                <Run Text="%"/>
                            </TextBlock>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- 其他功能 -->
            <GroupBox Header="其他功能" Margin="0,10" Padding="15">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                        <Button Command="{Binding GetSupportedFormatsCommand}"
                                Content="查看支持的导出格式"
                                Margin="5"
                                Height="30"
                                Padding="10,5"/>
                        
                        <Button Command="{Binding GetTaskStatusCommand}"
                                Content="查询当前任务状态"
                                Margin="5"
                                Height="30"
                                Padding="10,5"/>
                        
                        <Button Command="{Binding DownloadCurrentTaskCommand}"
                                Content="下载当前任务文件"
                                Margin="5"
                                Height="30"
                                Padding="10,5"/>
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button Command="{Binding TestApiEndpointsCommand}"
                                Content="🔍 测试下载接口"
                                Margin="5"
                                Height="30"
                                Padding="10,5"
                                Background="LightCoral"
                                ToolTip="测试各种可能的下载接口，帮助找到正确的下载路径"/>
                        
                        <Button Command="{Binding TestSignalRCommand}"
                                Content="📡 测试SignalR连接"
                                Margin="5"
                                Height="30"
                                Padding="10,5"
                                Background="Orange"
                                ToolTip="诊断SignalR连接状态和网络问题"/>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- 操作日志 -->
            <GroupBox Header="操作日志" Margin="0,10" Padding="15">
                <StackPanel>
                    <Button Command="{Binding ClearLogsCommand}"
                            Content="清空日志"
                            HorizontalAlignment="Right"
                            Height="25"
                            Width="80"
                            Margin="0,0,0,10"/>
                    
                    <Border Background="White"
                            BorderBrush="Gray"
                            BorderThickness="1"
                            Height="120">
                        <ScrollViewer VerticalScrollBarVisibility="Auto" 
                                      HorizontalScrollBarVisibility="Auto"
                                      Padding="5">
                            <ItemsControl ItemsSource="{Binding OperationLogs}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <TextBlock Margin="0,1" FontFamily="Consolas" FontSize="10">
                                            <Run Text="{Binding LevelText, Mode=OneWay}"/>
                                            <Run Text=" ["/>
                                            <Run Text="{Binding TimeText, Mode=OneWay}"/>
                                            <Run Text="] "/>
                                            <Run Text="{Binding Message, Mode=OneWay}"/>
                                        </TextBlock>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- 加载指示器 -->
            <GroupBox Header="正在处理" 
                      Margin="0,10" 
                      Padding="15"
                      Background="LightBlue"
                      Visibility="{Binding IsExporting, Converter={StaticResource BooleanToVisibilityConverter}}">
                <TextBlock Text="正在执行导出操作，请稍候..." 
                           FontSize="14"
                           FontWeight="Bold"
                           HorizontalAlignment="Center"/>
            </GroupBox>

        </StackPanel>
    </ScrollViewer>
</UserControl>