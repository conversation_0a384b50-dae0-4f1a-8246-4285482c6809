using System;
using System.Globalization;
using System.Windows.Data;

namespace WPF_MVVM_Test.MVVM_View.Dialog
{
    public class EditModeToSaveTextConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isEditMode = value is bool b && b;
            return isEditMode ? "保存" : "新增";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}