using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Process
{
    /// <summary>
    /// 工艺路线模型
    /// </summary>
    public class ProcessRoute : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _processRouteNumber = string.Empty;
        private string _processRouteName = string.Empty;
        private string _status = string.Empty;
        private string _description = string.Empty;
        private string _remark = string.Empty;
        private bool _isActive;
        private string _versionDescription = string.Empty;
        private string? _previousVersionId;
        private int _processStepCount;

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 工艺路线编号
        /// </summary>
        [JsonPropertyName("processRouteNumber")]
        public string ProcessRouteNumber
        {
            get => _processRouteNumber;
            set
            {
                if (_processRouteNumber != value)
                {
                    _processRouteNumber = value;
                    OnPropertyChanged(nameof(ProcessRouteNumber));
                }
            }
        }

        /// <summary>
        /// 工艺路线名称
        /// </summary>
        [JsonPropertyName("processRouteName")]
        public string ProcessRouteName
        {
            get => _processRouteName;
            set
            {
                if (_processRouteName != value)
                {
                    _processRouteName = value;
                    OnPropertyChanged(nameof(ProcessRouteName));
                }
            }
        }

        /// <summary>
        /// 状态
        /// </summary>
        [JsonPropertyName("status")]
        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged(nameof(Status));
                }
            }
        }

        /// <summary>
        /// 描述
        /// </summary>
        [JsonPropertyName("description")]
        public string Description
        {
            get => _description;
            set
            {
                if (_description != value)
                {
                    _description = value;
                    OnPropertyChanged(nameof(Description));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark
        {
            get => _remark;
            set
            {
                if (_remark != value)
                {
                    _remark = value;
                    OnPropertyChanged(nameof(Remark));
                }
            }
        }

        /// <summary>
        /// 是否激活
        /// </summary>
        [JsonPropertyName("isActive")]
        public bool IsActive
        {
            get => _isActive;
            set
            {
                if (_isActive != value)
                {
                    _isActive = value;
                    OnPropertyChanged(nameof(IsActive));
                }
            }
        }

        /// <summary>
        /// 版本描述
        /// </summary>
        [JsonPropertyName("versionDescription")]
        public string VersionDescription
        {
            get => _versionDescription;
            set
            {
                if (_versionDescription != value)
                {
                    _versionDescription = value;
                    OnPropertyChanged(nameof(VersionDescription));
                }
            }
        }

        /// <summary>
        /// 前一版本ID
        /// </summary>
        [JsonPropertyName("previousVersionId")]
        public string? PreviousVersionId
        {
            get => _previousVersionId;
            set
            {
                if (_previousVersionId != value)
                {
                    _previousVersionId = value;
                    OnPropertyChanged(nameof(PreviousVersionId));
                }
            }
        }

        /// <summary>
        /// 工艺步骤数量
        /// </summary>
        [JsonPropertyName("processStepCount")]
        public int ProcessStepCount
        {
            get => _processStepCount;
            set
            {
                if (_processStepCount != value)
                {
                    _processStepCount = value;
                    OnPropertyChanged(nameof(ProcessStepCount));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 工艺路线分页数据模型
    /// </summary>
    public class ProcessRoutePagedData
    {
        [JsonPropertyName("totalCount")]
        public int TotalCount { get; set; }

        [JsonPropertyName("totalPage")]
        public int TotalPage { get; set; }

        [JsonPropertyName("data")]
        public List<ProcessRoute> Data { get; set; } = new List<ProcessRoute>();
    }

    /// <summary>
    /// 工艺路线API响应模型
    /// </summary>
    public class ProcessRouteApiResponse
    {
        [JsonPropertyName("data")]
        public ProcessRoutePagedData Data { get; set; } = new ProcessRoutePagedData();

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 工艺路线查询参数模型
    /// </summary>
    public class ProcessRouteQueryParams
    {
        public string Status { get; set; } = "";
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 10;
    }
}