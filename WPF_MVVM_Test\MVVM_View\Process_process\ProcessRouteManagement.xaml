<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Process_process.ProcessRouteManagement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.Process_process"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Process_process"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             Background="{DynamicResource MaterialDesignPaper}"
             TextElement.Foreground="{DynamicResource MaterialDesignBody}"
             TextElement.FontWeight="Regular"
             TextElement.FontSize="13"
             TextOptions.TextFormattingMode="Ideal"
             TextOptions.TextRenderingMode="Auto"
             FontFamily="{DynamicResource MaterialDesignFont}">

    <UserControl.DataContext>
        <vm:ProcessRouteManagementViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 反向布尔到可见性转换器 -->
        <Style x:Key="InverseBooleanToVisibilityConverter" TargetType="FrameworkElement">
            <Setter Property="Visibility" Value="Visible"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 状态颜色转换器 -->
        <Style x:Key="StatusColorConverter" TargetType="Border">
            <Setter Property="Background" Value="#4CAF50"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="停用">
                    <Setter Property="Background" Value="#F44336"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 行索引转换器 -->
        <Style x:Key="RowIndexConverter" TargetType="TextBlock">
            <Setter Property="Text" Value="{Binding RelativeSource={RelativeSource AncestorType=DataGridRow}, Path=Header}"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题栏 -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="工艺路线管理"
                       FontSize="24"
                       FontWeight="Bold"
                       VerticalAlignment="Center"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="新增"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding AddCommand}"
                        Width="100"
                        Height="40"
                        Margin="0,0,10,0"/>
                
                <Button Content="刷新"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding RefreshCommand}"
                        Width="100"
                        Height="40"/>
            </StackPanel>
        </Grid>

        <!-- 搜索筛选区域 -->
        <materialDesign:Card Grid.Row="1" Padding="20" Margin="0,0,0,20">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                           Text="状态筛选："
                           VerticalAlignment="Center"
                           Margin="0,0,10,0"/>

                <ComboBox Grid.Column="1"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          ItemsSource="{Binding StatusOptions}"
                          SelectedItem="{Binding SelectedStatus}"
                          Height="60"
                          Margin="0,0,20,0"/>

                <Button Grid.Column="3"
                        Content="查询"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding SearchCommand}"
                        Width="100"
                        Height="40"/>
            </Grid>
        </materialDesign:Card>

        <!-- 数据表格 -->
        <materialDesign:Card Grid.Row="2" Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 加载指示器 -->
                <ProgressBar Grid.Row="0"
                             Style="{StaticResource MaterialDesignCircularProgressBar}"
                             Value="0"
                             IsIndeterminate="True"
                             Width="50"
                             Height="50"
                             HorizontalAlignment="Center"
                             VerticalAlignment="Center"
                             Visibility="{Binding IsLoading, Converter={StaticResource BooleanToVisibilityConverter}}"/>

                <!-- 数据表格 -->
                <DataGrid Grid.Row="0"
                          ItemsSource="{Binding ProcessRoutes}"
                          SelectedItem="{Binding SelectedProcessRoute}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserResizeRows="False"
                          CanUserSortColumns="True"
                          HeadersVisibility="Column"
                          GridLinesVisibility="All"
                          AlternatingRowBackground="#F8F8F8"
                          RowBackground="White"
                          BorderBrush="#E0E0E0"
                          BorderThickness="1"
                          FontSize="12">

                    <DataGrid.Style>
                        <Style TargetType="DataGrid" BasedOn="{StaticResource {x:Type DataGrid}}">
                            <Setter Property="Visibility" Value="Visible"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </DataGrid.Style>

                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True">
                            <DataGridTextColumn.Binding>
                                <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}" 
                                         Converter="{x:Static local:RowNumberConverter.Instance}"/>
                            </DataGridTextColumn.Binding>
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 工艺路线编号 -->
                        <DataGridTextColumn Header="工艺路线编号" 
                                            Binding="{Binding ProcessRouteNumber}" 
                                            Width="150"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 工艺路线名称 -->
                        <DataGridTextColumn Header="工艺路线名称" 
                                            Binding="{Binding ProcessRouteName}" 
                                            Width="200"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 状态 -->
                        <DataGridTemplateColumn Header="状态" Width="80">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="10"
                                            Padding="8,2"
                                            HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Setter Property="Background" Value="#4CAF50"/>
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="停用">
                                                        <Setter Property="Background" Value="#F44336"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}"
                                                   Foreground="White"
                                                   FontSize="10"
                                                   FontWeight="Bold"
                                                   HorizontalAlignment="Center"/>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 工艺步骤数 -->
                        <DataGridTextColumn Header="工艺步骤数" 
                                            Binding="{Binding ProcessStepCount}" 
                                            Width="100"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 版本描述 -->
                        <DataGridTextColumn Header="版本描述" 
                                            Binding="{Binding VersionDescription}" 
                                            Width="200"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 描述 -->
                        <DataGridTextColumn Header="描述" 
                                            Binding="{Binding Description}" 
                                            Width="*"
                                            IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#E8F5E8"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Left"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="TextWrapping" Value="Wrap"/>
                                    <Setter Property="Margin" Value="5"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- 分页控件 -->
                <Grid Grid.Row="1" Background="#F5F5F5" Height="50">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                               Text="{Binding PageInfo}"
                               VerticalAlignment="Center"
                               Margin="15,0,0,0"
                               FontSize="12"
                               Foreground="#666"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,15,0">
                        <Button Content="首页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding FirstPageCommand}"
                                Width="60"
                                Height="30"
                                Margin="0,0,5,0"/>
                        
                        <Button Content="上一页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding PreviousPageCommand}"
                                Width="60"
                                Height="30"
                                Margin="0,0,5,0"/>
                        
                        <Button Content="下一页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding NextPageCommand}"
                                Width="60"
                                Height="30"
                                Margin="0,0,5,0"/>
                        
                        <Button Content="末页"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Command="{Binding LastPageCommand}"
                                Width="60"
                                Height="30"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>