using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Process
{
    /// <summary>
    /// 工序模型
    /// </summary>
    public class ProcessStepModel : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _processStepName = string.Empty;
        private bool _isSelected = false;

        /// <summary>
        /// 工序ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 工序名称
        /// </summary>
        [JsonPropertyName("processStepName")]
        public string ProcessStepName
        {
            get => _processStepName;
            set
            {
                if (_processStepName != value)
                {
                    _processStepName = value;
                    OnPropertyChanged(nameof(ProcessStepName));
                }
            }
        }

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 工序API响应模型
    /// </summary>
    public class ProcessStepApiResponse
    {
        [JsonPropertyName("data")]
        public List<ProcessStepModel> Data { get; set; } = new List<ProcessStepModel>();

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }
}