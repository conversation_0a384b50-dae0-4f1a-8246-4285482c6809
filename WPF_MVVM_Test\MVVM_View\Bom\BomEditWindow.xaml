<Window x:Class="WPF_MVVM_Test.MVVM_View.Bom.BomEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Bom"
        mc:Ignorable="d"
        Title="BOM编辑"
        Height="600"
        Width="800"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- BOM编号颜色转换器 -->
        <local:BomNumberColorConverter x:Key="BomNumberColorConverter"/>
        
        <!-- BOM编号装饰转换器 -->
        <local:BomNumberDecorationConverter x:Key="BomNumberDecorationConverter"/>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题区域 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="基础信息" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="#2E3440"
                       HorizontalAlignment="Center"/>
            <Separator Margin="0,10,0,0"/>
        </StackPanel>

        <!-- 内容区域 -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 基础信息区域 -->
            <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,10">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- BOM编号 -->
                    <TextBox Grid.Row="0" Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="BOM编号"
                             Text="{Binding BomNumber, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,15"
                             Height="50"/>

                    <!-- 产品名称 -->
                    <TextBox Grid.Row="0" Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="产品名称"
                             Text="{Binding ProductName, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,15"
                             Height="50"/>

                    <!-- 版本 -->
                    <TextBox Grid.Row="1" Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="BOM版本"
                             Text="{Binding Version, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,15"
                             Height="50"/>

                    <!-- 颜色代码 -->
                    <TextBox Grid.Row="1" Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="颜色代码"
                             Text="{Binding ColorCode, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,15"
                             Height="50"/>

                    <!-- 单位 -->
                    <TextBox Grid.Row="2" Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="单位"
                             Text="{Binding Unit, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,15"
                             Height="50"/>

                    <!-- 日产量 -->
                    <TextBox Grid.Row="2" Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="日产量"
                             Text="{Binding DailyOutput, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,15"
                             Height="50"/>

                    <!-- 工艺路线名称 -->
                    <TextBox Grid.Row="3" Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="工艺路线名称"
                             Text="{Binding ProcessRouteName, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,15"
                             Height="50"/>

                    <!-- 项目数量 -->
                    <TextBox Grid.Row="3" Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="项目数量"
                             Text="{Binding ItemCount, UpdateSourceTrigger=PropertyChanged}"
                             Margin="10,0,0,15"
                             Height="50"/>

                    <!-- 是否系统编号 -->
                    <CheckBox Grid.Row="4" Grid.Column="0"
                              Content="是否系统编号"
                              IsChecked="{Binding IsSystemNumber}"
                              Margin="0,0,10,15"
                              VerticalAlignment="Center"/>

                    <!-- 是否默认 -->
                    <CheckBox Grid.Row="4" Grid.Column="1"
                              Content="是否默认"
                              IsChecked="{Binding IsDefault}"
                              Margin="10,0,0,15"
                              VerticalAlignment="Center"/>

                    <!-- 备注 -->
                    <TextBox Grid.Row="5" Grid.ColumnSpan="2"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="备注"
                             Text="{Binding Remark, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,0,0"
                             Height="80"
                             AcceptsReturn="True"
                             TextWrapping="Wrap"
                             VerticalScrollBarVisibility="Auto"/>
                </Grid>
            </materialDesign:Card>

            <!-- BOM组成区域 -->
            <materialDesign:Card Grid.Row="1" Padding="0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <!-- BOM组成标题和操作按钮 -->
                    <Grid Grid.Row="0" Background="#E8F5E8" Height="50">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0"
                                Content="选择BOM"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="#4CAF50"
                                Foreground="White"
                                Width="100"
                                Height="35"
                                Margin="15,0,10,0"
                                VerticalAlignment="Center"/>

                        <Button Grid.Column="1"
                                Content="清除"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Width="80"
                                Height="35"
                                Margin="0,0,10,0"
                                VerticalAlignment="Center"/>

                        <TextBlock Grid.Column="2"
                                   Text="BOM组成"
                                   FontSize="16"
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   HorizontalAlignment="Center"
                                   Foreground="#2E7D32"/>
                    </Grid>

                    <!-- BOM信息显示区域 -->
                    <Grid Grid.Row="1" Background="#F1F8E9" Height="40">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                                   Text="BOM编号："
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   Margin="15,0,5,0"/>

                        <TextBlock Grid.Column="1"
                                   Text="BOM00003"
                                   VerticalAlignment="Center"
                                   Margin="0,0,20,0"/>

                        <TextBlock Grid.Column="2"
                                   Text="BOM版本："
                                   FontWeight="Bold"
                                   VerticalAlignment="Center"
                                   Margin="0,0,5,0"/>

                        <TextBlock Grid.Column="3"
                                   Text="1.3"
                                   VerticalAlignment="Center"/>
                    </Grid>

                    <!-- BOM组成表格 -->
                    <DataGrid Grid.Row="2"
                              ItemsSource="{Binding BomCompositionItems}"
                              AutoGenerateColumns="False"
                              CanUserAddRows="False"
                              CanUserDeleteRows="False"
                              CanUserReorderColumns="False"
                              CanUserResizeRows="False"
                              CanUserSortColumns="False"
                              HeadersVisibility="Column"
                              GridLinesVisibility="All"
                              AlternatingRowBackground="#F8F8F8"
                              RowBackground="White"
                              BorderBrush="#E0E0E0"
                              BorderThickness="1"
                              FontSize="12">

                        <DataGrid.Columns>
                            <!-- 序号 -->
                            <DataGridTextColumn Header="序号" 
                                                Binding="{Binding Sequence}" 
                                                Width="60"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- 产品名称 -->
                            <DataGridTemplateColumn Header="产品名称" Width="120">
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center">
                                            <Button x:Name="ExpandButton"
                                                    Content="▼"
                                                    Width="20"
                                                    Height="20"
                                                    FontSize="8"
                                                    Background="Transparent"
                                                    BorderThickness="0"
                                                    Foreground="#4CAF50"
                                                    Margin="0,0,5,0"
                                                    VerticalAlignment="Center"
                                                    Visibility="{Binding HasSubBom, Converter={StaticResource BooleanToVisibilityConverter}}"/>
                                            <TextBlock Text="{Binding ProductName}" 
                                                       VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- 产品编号 -->
                            <DataGridTextColumn Header="产品编号" 
                                                Binding="{Binding ProductNumber}" 
                                                Width="100"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- 规格型号 -->
                            <DataGridTextColumn Header="规格型号" 
                                                Binding="{Binding Specification}" 
                                                Width="80"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- 单位 -->
                            <DataGridTextColumn Header="单位" 
                                                Binding="{Binding Unit}" 
                                                Width="60"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- BOM编号 -->
                            <DataGridTemplateColumn Header="BOM编号" Width="100">
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <TextBlock Text="{Binding BomNumber}" 
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   Foreground="{Binding BomNumber, Converter={StaticResource BomNumberColorConverter}}"
                                                   TextDecorations="{Binding BomNumber, Converter={StaticResource BomNumberDecorationConverter}}"/>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>

                            <!-- BOM版本 -->
                            <DataGridTextColumn Header="BOM版本" 
                                                Binding="{Binding BomVersion}" 
                                                Width="80"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- 使用量 -->
                            <DataGridTextColumn Header="使用量" 
                                                Binding="{Binding UsageQuantity}" 
                                                Width="80"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <!-- 使用比例 -->
                            <DataGridTextColumn Header="使用比例" 
                                                Binding="{Binding UsageRatio}" 
                                                Width="80"
                                                IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#E8F5E8"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#C8E6C9"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </materialDesign:Card>
        </Grid>


        <!-- 按钮区域 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right" 
                    Margin="0,20,0,0">
            <Button Content="保存"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="100"
                    Height="40"
                    Margin="0,0,10,0"
                    Click="SaveButton_Click"/>
            
            <Button Content="取消"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100"
                    Height="40"
                    Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>