<Window x:Class="WPF_MVVM_Test.MVVM_View.ProductionOrder.ScheduleProductionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.ProductionOrder"
        mc:Ignorable="d"
        Title="工单排产" Height="900" Width="950" MinHeight="850" MinWidth="900"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize">
    <Window.Resources>
        <!-- 布尔值到背景色转换器 -->
        <local:BooleanToBackgroundConverter x:Key="BooleanToBackgroundConverter"/>
        
        <!-- 布尔值到边框厚度转换器 -->
        <local:BooleanToBorderThicknessConverter x:Key="BooleanToBorderThicknessConverter"/>
        
        <!-- 布尔值到前景色转换器 -->
        <local:BooleanToForegroundConverter x:Key="BooleanToForegroundConverter"/>
        
        <!-- 布尔值到字体粗细转换器 -->
        <local:BooleanToFontWeightConverter x:Key="BooleanToFontWeightConverter"/>

        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Height" Value="32"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- 只读文本框样式 -->
        <Style x:Key="ReadOnlyTextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Height" Value="32"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="BorderBrush" Value="#E8E8E8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="#F5F5F5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="IsReadOnly" Value="True"/>
        </Style>

        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
        </Style>

        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}" CornerRadius="4" Padding="16,8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#40A9FF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#096DD9"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Background" Value="White"/>
            <Setter Property="Foreground" Value="#595959"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4" Padding="16,8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="BorderBrush" Value="#40A9FF"/>
                                <Setter Property="Foreground" Value="#40A9FF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    <Grid Background="#F5F5F5" Margin="10">
        <Border Background="White" CornerRadius="8" Padding="20">
            <!-- 添加 ScrollViewer 包装整个内容 -->
            <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Disabled">
                <StackPanel>
                    <!-- 标题 -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                        <TextBlock Text="📋" FontSize="22" Margin="0,0,10,0"/>
                        <TextBlock Text="工单排产" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    </StackPanel>

                    <!-- 基础信息区域（只读） -->
                    <Border Background="#FAFAFA" BorderBrush="#E8E8E8" BorderThickness="1" CornerRadius="6" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="📄 基础信息" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12" Foreground="#262626"/>

                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行 -->
                                <TextBlock Text="工单编号：" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding OrderNumber, Mode=OneWay}" Grid.Row="0" Grid.Column="1" Style="{StaticResource ReadOnlyTextBoxStyle}" Margin="0,0,15,10"/>
                                <TextBlock Text="工单名称：" Grid.Row="0" Grid.Column="2" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding ProductName, Mode=OneWay}" Grid.Row="0" Grid.Column="3" Style="{StaticResource ReadOnlyTextBoxStyle}" Margin="0,0,0,10"/>

                                <!-- 第二行 -->
                                <TextBlock Text="产品编号：" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding ProductNumber, Mode=OneWay}" Grid.Row="1" Grid.Column="1" Style="{StaticResource ReadOnlyTextBoxStyle}" Margin="0,0,15,10"/>
                                <TextBlock Text="规格型号：" Grid.Row="1" Grid.Column="2" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Specification, Mode=OneWay}" Grid.Row="1" Grid.Column="3" Style="{StaticResource ReadOnlyTextBoxStyle}" Margin="0,0,0,10"/>

                                <!-- 第三行 -->
                                <TextBlock Text="单位：" Grid.Row="2" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding Unit, Mode=OneWay}" Grid.Row="2" Grid.Column="1" Style="{StaticResource ReadOnlyTextBoxStyle}" Margin="0,0,15,0"/>
                                <TextBlock Text="BOM编码：" Grid.Row="2" Grid.Column="2" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding BomCode, Mode=OneWay}" Grid.Row="2" Grid.Column="3" Style="{StaticResource ReadOnlyTextBoxStyle}"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- 排产信息区域（可编辑） -->
                    <Border Background="#F6FFED" BorderBrush="#B7EB8F" BorderThickness="1" CornerRadius="6" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="⚙️ 排产信息" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12" Foreground="#262626"/>

                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="120"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <!-- 第一行 -->
                                <TextBlock Text="计划数量：" Grid.Row="0" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                <TextBox Text="{Binding PlanQuantity, UpdateSourceTrigger=PropertyChanged}"
                                         Grid.Row="0" Grid.Column="1" Style="{StaticResource InputTextBoxStyle}" Margin="0,0,15,10"/>
                                <TextBlock Text="开工时间：" Grid.Row="0" Grid.Column="2" Style="{StaticResource LabelStyle}"/>
                                <DatePicker SelectedDate="{Binding StartTime, Mode=TwoWay}"
                                            Grid.Row="0" Grid.Column="3" Height="32" Margin="0,0,0,10"/>

                                <!-- 第二行 -->
                                <TextBlock Text="完工时间：" Grid.Row="1" Grid.Column="0" Style="{StaticResource LabelStyle}"/>
                                <DatePicker SelectedDate="{Binding EndTime, Mode=TwoWay}"
                                            Grid.Row="1" Grid.Column="1" Height="32" Margin="0,0,15,0"/>
                            </Grid>

                            <!-- 备注区域 -->
                            <StackPanel Margin="0,0,0,0">
                                <TextBlock Text="备注：" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding Remark, UpdateSourceTrigger=PropertyChanged}"
                                         Height="80" Style="{StaticResource InputTextBoxStyle}"
                                         AcceptsReturn="True" TextWrapping="Wrap"
                                         VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- 物料清单区域 -->
                    <Border Background="#FFF7E6" BorderBrush="#FFD591" BorderThickness="1" CornerRadius="6" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <TextBlock Text="📦 物料清单" FontSize="15" FontWeight="SemiBold" Margin="0,0,0,12" Foreground="#262626"/>

                            <DataGrid ItemsSource="{Binding BomItems}" AutoGenerateColumns="False"
                                      Height="120" CanUserAddRows="False" CanUserDeleteRows="False"
                                      IsReadOnly="True" GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column" Background="White">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="物料名称" Binding="{Binding MaterialName}" Width="100"/>
                                    <DataGridTextColumn Header="物料编号" Binding="{Binding MaterialNumber}" Width="80"/>
                                    <DataGridTextColumn Header="规格" Binding="{Binding Specification}" Width="100"/>
                                    <DataGridTextColumn Header="需求数量" Binding="{Binding RequiredQuantity}" Width="80"/>
                                    <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="50"/>
                                    <DataGridTextColumn Header="库存数量" Binding="{Binding AvailableQuantity}" Width="80"/>
                                    <DataGridTextColumn Header="状态" Binding="{Binding Status}" Width="60"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                    <!-- 工艺流程区域 -->
                    <Border Background="#F0F9FF" BorderBrush="#91D5FF" BorderThickness="1" CornerRadius="6" Padding="15" Margin="0,0,0,15">
                        <StackPanel>
                            <!-- 工艺流程标题和路线信息 -->
                            <Grid Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                            
                                <TextBlock Grid.Column="0" Text="⚙️ 工艺流程" FontSize="15" FontWeight="SemiBold" Foreground="#262626"/>
                            
                                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="工艺路线名称：" Margin="20,0,5,0"/>
                                    <TextBlock Text="{Binding ProcessRoute.RouteName}" FontWeight="Bold"/>
                                    <TextBlock Text="工艺路线编号：" Margin="40,0,5,0"/>
                                    <TextBlock Text="{Binding ProcessRoute.RouteCode}" FontWeight="Bold"/>
                                </StackPanel>
                            
                                <Border Grid.Column="2" Background="#FFA500" CornerRadius="10" Padding="5,2">
                                    <TextBlock Text="2" Foreground="White" FontSize="12" HorizontalAlignment="Center"/>
                                </Border>
                            </Grid>

                            <!-- 工序选项卡 -->
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <ItemsControl ItemsSource="{Binding ProcessItems}">
                                    <ItemsControl.ItemsPanel>
                                        <ItemsPanelTemplate>
                                            <StackPanel Orientation="Horizontal"/>
                                        </ItemsPanelTemplate>
                                    </ItemsControl.ItemsPanel>
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border Background="{Binding IsSelected, Converter={StaticResource BooleanToBackgroundConverter}}" 
                                                    BorderBrush="#1890FF" 
                                                    BorderThickness="{Binding IsSelected, Converter={StaticResource BooleanToBorderThicknessConverter}}"
                                                    CornerRadius="20" 
                                                    Padding="15,8" 
                                                    Margin="0,0,10,0"
                                                    Cursor="Hand">
                                                <Border.InputBindings>
                                                    <MouseBinding Command="{Binding DataContext.SelectProcessCommand, RelativeSource={RelativeSource AncestorType=Window}}" 
                                                                 CommandParameter="{Binding}" 
                                                                 MouseAction="LeftClick"/>
                                                </Border.InputBindings>
                                                <StackPanel Orientation="Horizontal">
                                                    <Border Background="#1890FF" CornerRadius="10" Width="20" Height="20" Margin="0,0,8,0">
                                                        <TextBlock Text="{Binding Sequence}" Foreground="White" FontSize="12" 
                                                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                    <TextBlock Text="{Binding ProcessName}" 
                                                              Foreground="{Binding IsSelected, Converter={StaticResource BooleanToForegroundConverter}}"
                                                              FontWeight="{Binding IsSelected, Converter={StaticResource BooleanToFontWeightConverter}}"/>
                                                </StackPanel>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>

                            <!-- 操作按钮 -->
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <Button Content="新增" Background="#1890FF" Foreground="White" BorderThickness="0" 
                                        Padding="15,8" Margin="0,0,10,0">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    CornerRadius="4" 
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                </Button>
                                <Button Content="编辑" Background="Transparent" Foreground="#1890FF" BorderBrush="#1890FF" BorderThickness="1" 
                                        Padding="15,8" Margin="0,0,10,0">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="4" 
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                </Button>
                                <Button Content="删除" Background="Transparent" Foreground="#1890FF" BorderBrush="#1890FF" BorderThickness="1" 
                                        Padding="15,8">
                                    <Button.Template>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}" 
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="4" 
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                            </Border>
                                        </ControlTemplate>
                                    </Button.Template>
                                </Button>
                            </StackPanel>

                            <!-- 任务列表 -->
                            <DataGrid ItemsSource="{Binding SelectedProcess.Tasks}" 
                                      AutoGenerateColumns="False" 
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      IsReadOnly="True"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      Height="200"
                                      Background="White">
                                <DataGrid.Columns>
                                    <DataGridTemplateColumn Header="" Width="40">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <CheckBox HorizontalAlignment="Center"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                    <DataGridTextColumn Header="序号" Binding="{Binding Index}" Width="60"/>
                                    <DataGridTextColumn Header="任务编号" Binding="{Binding TaskCode}" Width="100"/>
                                    <DataGridTextColumn Header="任务名称" Binding="{Binding TaskName}" Width="100"/>
                                    <DataGridTextColumn Header="站点名称" Binding="{Binding StationName}" Width="100"/>
                                    <DataGridTextColumn Header="站点编号" Binding="{Binding StationCode}" Width="100"/>
                                    <DataGridTextColumn Header="计划数量" Binding="{Binding PlanQuantity}" Width="80"/>
                                    <DataGridTextColumn Header="计划开工时间" Binding="{Binding PlanStartTime, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                    <DataGridTextColumn Header="计划完工时间" Binding="{Binding PlanEndTime, StringFormat=yyyy-MM-dd HH:mm}" Width="140"/>
                                    <DataGridTemplateColumn Header="任务颜色" Width="80">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <Rectangle Fill="#1890FF" Width="20" Height="15" RadiusX="2" RadiusY="2"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </StackPanel>
                    </Border>

                    <!-- 按钮区域 -->
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
                        <Button Content="✅ 确定排产" Width="120" Height="40" Margin="0,0,15,0"
                                Style="{StaticResource PrimaryButtonStyle}" Command="{Binding ConfirmCommand}"/>
                        <Button Content="❌ 取消" Width="100" Height="40"
                                Style="{StaticResource SecondaryButtonStyle}" Command="{Binding CancelCommand}"/>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>
    </Grid>
</Window>
