using System;
using System.Collections.ObjectModel;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model;

namespace WPF_MVVM_Test.MVVM_ViewModel
{
    /// <summary>
    /// 报表管理页面的ViewModel
    /// 负责报表相关的业务逻辑和数据绑定
    /// </summary>
    public class ReportManagementViewModel : BaseViewModel
    {
        private string _selectedReportType = "用户报表";
        private DateTime _startDate = DateTime.Now.AddDays(-30);
        private DateTime _endDate = DateTime.Now;
        private string _reportStatus = "准备生成报表";

        /// <summary>
        /// 报表类型集合
        /// </summary>
        public ObservableCollection<string> ReportTypes { get; }

        /// <summary>
        /// 报表数据集合
        /// </summary>
        public ObservableCollection<ReportItem> ReportData { get; }

        /// <summary>
        /// 选中的报表类型
        /// </summary>
        public string SelectedReportType
        {
            get => _selectedReportType;
            set => SetProperty(ref _selectedReportType, value);
        }

        /// <summary>
        /// 开始日期
        /// </summary>
        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        /// <summary>
        /// 结束日期
        /// </summary>
        public DateTime EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        /// <summary>
        /// 报表状态
        /// </summary>
        public string ReportStatus
        {
            get => _reportStatus;
            set => SetProperty(ref _reportStatus, value);
        }

        /// <summary>
        /// 生成报表命令
        /// </summary>
        public ICommand GenerateReportCommand { get; }

        /// <summary>
        /// 导出报表命令
        /// </summary>
        public ICommand ExportReportCommand { get; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReportManagementViewModel()
        {
            // 初始化报表类型
            ReportTypes = new ObservableCollection<string>
            {
                "用户报表",
                "系统日志报表",
                "性能统计报表",
                "错误分析报表"
            };

            // 初始化报表数据
            ReportData = new ObservableCollection<ReportItem>();
            InitializeTestData();

            // 初始化命令
            GenerateReportCommand = CreateCommand(ExecuteGenerateReport, CanExecuteGenerateReport);
            ExportReportCommand = CreateCommand(ExecuteExportReport, CanExecuteExportReport);
        }

        /// <summary>
        /// 初始化测试数据
        /// </summary>
        private void InitializeTestData()
        {
            // 清空现有数据
            ReportData.Clear();

            // 添加模拟数据
            ReportData.Add(new ReportItem
            {
                Id = "R001",
                Name = "系统登录统计",
                Type = "用户报表",
                Date = DateTime.Now.AddDays(-10),
                Value = 156,
                Status = "已完成"
            });

            ReportData.Add(new ReportItem
            {
                Id = "R002",
                Name = "系统性能分析",
                Type = "性能统计报表",
                Date = DateTime.Now.AddDays(-5),
                Value = 89.5,
                Status = "已完成"
            });

            ReportData.Add(new ReportItem
            {
                Id = "R003",
                Name = "错误日志汇总",
                Type = "错误分析报表",
                Date = DateTime.Now.AddDays(-2),
                Value = 23,
                Status = "处理中"
            });

            ReportData.Add(new ReportItem
            {
                Id = "R004",
                Name = "用户活跃度分析",
                Type = "用户报表",
                Date = DateTime.Now.AddDays(-1),
                Value = 78.3,
                Status = "已完成"
            });

            ReportData.Add(new ReportItem
            {
                Id = "R005",
                Name = "系统资源占用",
                Type = "性能统计报表",
                Date = DateTime.Now,
                Value = 45.2,
                Status = "处理中"
            });
        }

        /// <summary>
        /// 执行生成报表
        /// </summary>
        private void ExecuteGenerateReport()
        {
            try
            {
                ReportStatus = "正在生成报表...";
                
                // 模拟报表生成过程
                System.Threading.Tasks.Task.Run(async () =>
                {
                    try 
                    {
                        await System.Threading.Tasks.Task.Delay(2000); // 模拟耗时操作
                        
                        // 在UI线程更新状态
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            // 更新测试数据
                            InitializeTestData();
                            
                            ReportStatus = $"报表生成完成！类型：{SelectedReportType}，时间范围：{StartDate:yyyy-MM-dd} 至 {EndDate:yyyy-MM-dd}";
                        });
                    }
                    catch
                    {
                        // 在UI线程更新状态
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            ReportStatus = "生成报表过程中发生错误";
                        });
                    }
                });
            }
            catch (Exception)
            {
                ReportStatus = "生成报表失败，请稍后再试";
            }
        }

        /// <summary>
        /// 判断是否可以生成报表
        /// </summary>
        private bool CanExecuteGenerateReport()
        {
            return !string.IsNullOrEmpty(SelectedReportType) && StartDate <= EndDate;
        }

        /// <summary>
        /// 执行导出报表
        /// </summary>
        private void ExecuteExportReport()
        {
            try
            {
                ReportStatus = "正在导出报表...";
                
                // 模拟导出过程
                System.Threading.Tasks.Task.Run(async () =>
                {
                    try
                    {
                        await System.Threading.Tasks.Task.Delay(1000);
                        
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            ReportStatus = "报表导出成功！已保存到桌面。";
                        });
                    }
                    catch
                    {
                        System.Windows.Application.Current.Dispatcher.Invoke(() =>
                        {
                            ReportStatus = "导出报表过程中发生错误";
                        });
                    }
                });
            }
            catch (Exception)
            {
                ReportStatus = "导出报表失败，请稍后再试";
            }
        }

        /// <summary>
        /// 判断是否可以导出报表
        /// </summary>
        private bool CanExecuteExportReport()
        {
            return ReportData.Count > 0;
        }
    }

    /// <summary>
    /// 报表项数据模型
    /// </summary>
    public class ReportItem
    {
        public string Id { get; set; }
        public string Name { get; set; }
        public string Type { get; set; }
        public DateTime Date { get; set; }
        public double Value { get; set; }
        public string Status { get; set; }
    }
}
