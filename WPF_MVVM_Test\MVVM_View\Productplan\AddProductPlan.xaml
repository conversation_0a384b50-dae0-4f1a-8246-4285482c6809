<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Productplan.AddProductPlan"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Productplan"
        xmlns:converters="clr-namespace:WPF_MVVM_Test.MVVM_View.Converters"
        mc:Ignorable="d">



    
    <UserControl.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 编辑模式到保存按钮文本转换器 -->
        <converters:EditModeToSaveTextConverter x:Key="EditModeToSaveTextConverter"/>
        
        <!-- 必填字段标记样式 -->
        <Style x:Key="RequiredStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="Text" Value="*"/>
            <Setter Property="Foreground" Value="Red"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,5,0"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
        
        <!-- 输入框样式 -->
        <Style x:Key="InputTextBoxStyle" TargetType="{x:Type TextBox}">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderBrush" Value="#D9D9D9"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
        </Style>
        
        <!-- 标签样式 -->
        <Style x:Key="LabelStyle" TargetType="{x:Type TextBlock}">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,10,0"/>
            <Setter Property="Foreground" Value="#333"/>
            <Setter Property="TextWrapping" Value="NoWrap"/>
        </Style>
        
        <!-- 按钮样式 -->
        <Style x:Key="PrimaryButtonStyle" TargetType="{x:Type Button}">
            <Setter Property="Background" Value="#1890FF"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type Button}">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <!-- 主内容区域 -->
        <Border Background="White" CornerRadius="8" Margin="20" Padding="30">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- 页面标题 -->
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,30">
                    <TextBlock Text="{Binding PageTitle}" FontSize="24" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button Content="← 返回列表" Background="Transparent" Foreground="#1890FF"
                            BorderThickness="0" Cursor="Hand" Margin="20,0,0,0"
                            Click="BackToList_Click" FontSize="14"/>
                </StackPanel>

                <!-- 基础信息标题 -->
                <TextBlock Grid.Row="1" Text="基础信息" FontSize="16" FontWeight="Bold" 
                           Foreground="#1890FF" Margin="0,0,0,20"/>

                <!-- 表单内容 -->
                <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
                    <StackPanel Margin="0,0,0,20">
                        
                        <!-- 第一行：计划编号 和 计划名称 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 计划编号 -->
                            <StackPanel Grid.Column="0">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <TextBlock Style="{StaticResource RequiredStyle}"/>
                                    <TextBlock Text="计划编号" Style="{StaticResource LabelStyle}"/>
                                </StackPanel>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Grid.Column="0" Text="{Binding PlanNumber, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                             Style="{StaticResource InputTextBoxStyle}"/>
                                    <Button Grid.Column="1" Content="系统编号" Width="90" Height="35" Margin="10,0,0,0"
                                            Background="#1890FF" Foreground="White" BorderThickness="0"
                                            Command="{Binding GenerateNumberCommand}" Cursor="Hand"/>
                                </Grid>
                            </StackPanel>
                            
                            <!-- 计划名称 -->
                            <StackPanel Grid.Column="2">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <TextBlock Style="{StaticResource RequiredStyle}"/>
                                    <TextBlock Text="计划名称" Style="{StaticResource LabelStyle}"/>
                                </StackPanel>
                                <TextBox Text="{Binding PlanName, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Style="{StaticResource InputTextBoxStyle}"/>
                            </StackPanel>
                        </Grid>

                        <!-- 第二行：来源类型 和 订单编号 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 来源类型 -->
                            <StackPanel Grid.Column="0">
                                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                                    <TextBlock Style="{StaticResource RequiredStyle}"/>
                                    <TextBlock Text="来源类型" Style="{StaticResource LabelStyle}"/>
                                </StackPanel>
                                <ComboBox Height="35" ItemsSource="{Binding SourceTypes}"
                                          SelectedValue="{Binding SelectedSourceType, Mode=TwoWay}"
                                          DisplayMemberPath="Text" SelectedValuePath="Value"/>
                            </StackPanel>
                            
                            <!-- 订单编号 -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="订单编号" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Grid.Column="0" Text="{Binding OrderNumber, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                             Style="{StaticResource InputTextBoxStyle}" IsReadOnly="True" Background="#F5F5F5"/>
                                    <Button Grid.Column="1" Content="🔍" Width="50" Height="35" Margin="5,0,0,0"
                                            Background="#1890FF" Foreground="White" BorderThickness="0"
                                            Command="{Binding SelectOrderCommand}"/>
                                </Grid>
                            </StackPanel>
                        </Grid>

                        <!-- 第三行：成品名称 和 成品编号 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 成品名称 -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="成品名称" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox Grid.Column="0" Text="{Binding ProductName, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                             Style="{StaticResource InputTextBoxStyle}" IsReadOnly="True" Background="#F5F5F5"/>
                                    <Button Grid.Column="1" Content="🔍" Width="50" Height="35" Margin="5,0,0,0"
                                            Background="#1890FF" Foreground="White" BorderThickness="0"
                                            Command="{Binding SelectProductCommand}"/>
                                </Grid>
                            </StackPanel>
                            
                            <!-- 成品编号 -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="成品编号" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding ProductNumber, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Style="{StaticResource InputTextBoxStyle}" IsReadOnly="True" Background="#F5F5F5"/>
                            </StackPanel>
                        </Grid>

                        <!-- 第四行：规格型号 和 成品类型 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 规格型号 -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="规格型号" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding Specification, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Style="{StaticResource InputTextBoxStyle}" IsReadOnly="True" Background="#F5F5F5"/>
                            </StackPanel>
                            
                            <!-- 成品类型 -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="成品类型" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding ProductType, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Style="{StaticResource InputTextBoxStyle}" IsReadOnly="True" Background="#F5F5F5"/>
                            </StackPanel>
                        </Grid>

                        <!-- 第五行：单位 和 计划数量 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 单位 -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="单位" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding Unit, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Style="{StaticResource InputTextBoxStyle}" IsReadOnly="True" Background="#F5F5F5"/>
                            </StackPanel>
                            
                            <!-- 计划数量 -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="计划数量" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding PlanQuantity, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Style="{StaticResource InputTextBoxStyle}"/>
                            </StackPanel>
                        </Grid>

                        <!-- 第六行：开工时间 和 完工时间 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 开工时间 -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="开工时间" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <DatePicker Height="35" SelectedDate="{Binding PlanStartTime, Mode=TwoWay}"
                                            BorderBrush="#D9D9D9" BorderThickness="1"
                                            FontSize="14" Padding="10,8"
                                            SelectedDateFormat="Short"
                                            DisplayDateStart="{x:Static sys:DateTime.Today}"/>
                            </StackPanel>
                            
                            <!-- 完工时间 -->
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="完工时间" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <DatePicker Height="35" SelectedDate="{Binding PlanEndTime, Mode=TwoWay}"
                                            BorderBrush="#D9D9D9" BorderThickness="1"
                                            FontSize="14" Padding="10,8"
                                            SelectedDateFormat="Short"
                                            DisplayDateStart="{Binding PlanStartTime}"/>
                            </StackPanel>
                        </Grid>

                        <!-- 第七行：需求日期 -->
                        <Grid Margin="0,0,0,20">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- 需求日期 -->
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="需求日期" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <DatePicker Height="35" SelectedDate="{Binding RequiredDate, Mode=TwoWay}"
                                            BorderBrush="#D9D9D9" BorderThickness="1"
                                            FontSize="14" Padding="10,8"
                                            SelectedDateFormat="Short"
                                            DisplayDateStart="{x:Static sys:DateTime.Today}"/>
                            </StackPanel>
                        </Grid>

                        <!-- 第八行：备注 -->
                        <Grid Margin="0,0,0,20">
                            <StackPanel>
                                <TextBlock Text="备注" Style="{StaticResource LabelStyle}" Margin="0,0,0,8"/>
                                <TextBox Text="{Binding Remark, UpdateSourceTrigger=PropertyChanged, Mode=TwoWay}"
                                         Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                                         VerticalContentAlignment="Top" Padding="10"
                                         BorderBrush="#D9D9D9" BorderThickness="1"/>
                            </StackPanel>
                        </Grid>
                        
                    </StackPanel>
                </ScrollViewer>

                <!-- 附件上传区域 -->
                <Expander Grid.Row="3" Header="附件" IsExpanded="False" Margin="0,20,0,0"
                          BorderBrush="#E0E0E0" BorderThickness="1" Background="White"
                          MinHeight="80" MaxHeight="400">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 文件上传区域 -->
                        <Border Grid.Row="0" BorderBrush="#D9D9D9" BorderThickness="2"
                                Background="#FAFAFA" Height="120" Margin="0,0,0,15"
                                CornerRadius="4">
                            
                            <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                                <TextBlock Text="📁" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock Text="点击或拖拽文件到此区域上传" FontSize="14"
                                           HorizontalAlignment="Center" Foreground="#666"/>
                                <TextBlock Text="支持 docx, xls, PDF, rar, zip, PNG, JPG 等类型文件"
                                           FontSize="12" HorizontalAlignment="Center"
                                           Foreground="#999" Margin="0,5,0,0"/>
                                <Button Content="选择文件" Background="#1890FF" Foreground="White"
                                        BorderThickness="0" Padding="15,8" Margin="0,10,0,0"
                                        Command="{Binding SelectFileCommand}"/>
                            </StackPanel>
                        </Border>

                        <!-- 已上传文件列表 -->
                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" MaxHeight="200">
                            <ItemsControl ItemsSource="{Binding UploadedFiles}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="#E0E0E0" BorderThickness="1"
                                                Background="White" Margin="0,2" Padding="10">
                                            <Grid>
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                    <ColumnDefinition Width="Auto"/>
                                                </Grid.ColumnDefinitions>

                                                <TextBlock Grid.Column="0" Text="📄" FontSize="16"
                                                           VerticalAlignment="Center" Margin="0,0,10,0"/>
                                                <TextBlock Grid.Column="1" Text="{Binding FileName}"
                                                           VerticalAlignment="Center" FontSize="14"/>
                                                <TextBlock Grid.Column="2" Text="{Binding FileSize}"
                                                           VerticalAlignment="Center" FontSize="12"
                                                           Foreground="#666" Margin="10,0"/>
                                                <Button Grid.Column="3" Content="❌"
                                                        Background="Transparent" BorderThickness="0"
                                                        Foreground="Red" FontSize="12"
                                                        Command="{Binding DataContext.RemoveFileCommand,
                                                                 RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"/>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                    </Grid>
                </Expander>

                <!-- BOM组件区域 -->
                <Expander Grid.Row="4" Header="BOM组件" IsExpanded="False" Margin="0,10,0,0"
                          BorderBrush="#E0E0E0" BorderThickness="1" Background="White"
                          MinHeight="80" MaxHeight="300">
                    <Grid Margin="20">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- BOM选择按钮 -->
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                            <Button Content="选择BOM" Background="#1890FF" Foreground="White"
                                    BorderThickness="0" Padding="15,8" Margin="0,0,10,0"
                                    Command="{Binding SelectBomCommand}"/>
                            <TextBlock Text="{Binding SelectedBomInfo}" VerticalAlignment="Center"
                                       FontSize="14" Foreground="#666" Margin="10,0,0,0"/>
                        </StackPanel>

                        <!-- 警告信息 -->
                        <Border Grid.Row="1" Background="#FFF7E6" BorderBrush="#FFD591"
                                BorderThickness="1" Padding="10" CornerRadius="4" Margin="0,0,0,10"
                                Visibility="{Binding ShowBomWarning, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="⚠️" FontSize="16" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <TextBlock Text="请先选择成品！" FontSize="14" VerticalAlignment="Center" Foreground="#D46B08"/>
                            </StackPanel>
                        </Border>

                        <!-- BOM组件列表区域 -->
                        <Border Grid.Row="2" BorderBrush="#E0E0E0" BorderThickness="1" 
                                Background="#FAFAFA" MinHeight="100" CornerRadius="4"
                                Visibility="{Binding HasBomComponents, Converter={StaticResource BooleanToVisibilityConverter}}">
                            <ScrollViewer VerticalScrollBarVisibility="Auto" MaxHeight="200">
                                <ItemsControl ItemsSource="{Binding BomComponents}" Margin="10">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <Border BorderBrush="#E0E0E0" BorderThickness="1"
                                                    Background="White" Margin="0,2" Padding="10">
                                                <Grid>
                                                    <Grid.ColumnDefinitions>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="*"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                        <ColumnDefinition Width="Auto"/>
                                                    </Grid.ColumnDefinitions>

                                                    <TextBlock Grid.Column="0" Text="🔧" FontSize="16"
                                                               VerticalAlignment="Center" Margin="0,0,10,0"/>
                                                    <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                                        <TextBlock Text="{Binding ComponentName}" FontSize="14" FontWeight="Bold"/>
                                                        <TextBlock Text="{Binding ComponentCode}" FontSize="12" Foreground="#666"/>
                                                    </StackPanel>
                                                    <TextBlock Grid.Column="2" Text="{Binding Quantity}" 
                                                               VerticalAlignment="Center" FontSize="12"
                                                               Foreground="#666" Margin="10,0"/>
                                                    <TextBlock Grid.Column="3" Text="{Binding Unit}"
                                                               VerticalAlignment="Center" FontSize="12"
                                                               Foreground="#666" Margin="5,0"/>
                                                </Grid>
                                            </Border>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </ScrollViewer>
                        </Border>
                        <!-- BOM树形结构展示 -->
                        <TreeView Grid.Row="3" ItemsSource="{Binding BomTree}" Margin="0,10,0,0">
                            <TreeView.ItemTemplate>
                                <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="{Binding DisplayName}" FontWeight="Bold"/>
                                        <TextBlock Text="（" />
                                        <TextBlock Text="{Binding DisplayProductNumber}" />
                                        <TextBlock Text="，" />
                                        <TextBlock Text="{Binding DisplaySpecification}" />
                                        <TextBlock Text="，" />
                                        <TextBlock Text="{Binding UsageQuantity}" />
                                        <TextBlock Text="{Binding Unit}" />
                                        <TextBlock Text="）" />
                                    </StackPanel>
                                </HierarchicalDataTemplate>
                            </TreeView.ItemTemplate>
                        </TreeView>
                    </Grid>
                </Expander>

                <!-- 底部按钮区域 -->
                <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                    <Button Content="{Binding IsEditMode, Converter={StaticResource EditModeToSaveTextConverter}}" 
                            Width="100" Height="40" Margin="10,0"
                            Background="#52C41A" Foreground="White" BorderThickness="0"
                            Command="{Binding SaveCommand}" FontSize="14" FontWeight="Bold"/>
                    <Button Content="取消" Width="100" Height="40" Margin="10,0"
                            Background="#D9D9D9" Foreground="#666" BorderThickness="0"
                            Click="BackToList_Click" FontSize="14"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</UserControl>






























