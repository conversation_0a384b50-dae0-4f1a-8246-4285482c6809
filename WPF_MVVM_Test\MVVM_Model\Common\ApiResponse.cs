using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.Common
{
    /// <summary>
    /// 通用API响应模型
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class ApiResponse<T>
    {
        public T Data { get; set; }
        public bool IsSuc { get; set; }
        public int Code { get; set; }
        public string Msg { get; set; }
    }

    /// <summary>
    /// 分页数据模型
    /// </summary>
    /// <typeparam name="T">数据类型</typeparam>
    public class PagedData<T>
    {
        public int TotalCount { get; set; }
        public int TotalPage { get; set; }
        public List<T> Data { get; set; }
    }
} 