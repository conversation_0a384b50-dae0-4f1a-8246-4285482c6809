<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Material.SimpleTreeComboBox"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Material"
             mc:Ignorable="d" 
             d:DesignHeight="60" d:DesignWidth="300">

    <UserControl.Resources>
        <local:LevelToWidthConverter x:Key="LevelToWidthConverter"/>
    </UserControl.Resources>

    <Grid>
        <ComboBox x:Name="CategoryComboBox"
                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                  materialDesign:HintAssist.Hint="物料分类 *"
                  Height="60"
                  IsEditable="False">
            
            <ComboBox.ItemTemplate>
                <DataTemplate>
                    <StackPanel Orientation="Horizontal">
                        <!-- 层级缩进 -->
                        <Border Width="{Binding Level, Converter={StaticResource LevelToWidthConverter}}" />
                        
                        <!-- 层级指示器 -->
                        <Rectangle Width="2" Height="16" Fill="#CCCCCC" Margin="0,0,5,0">
                            <Rectangle.Style>
                                <Style TargetType="Rectangle">
                                    <Setter Property="Visibility" Value="Collapsed"/>
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Level}" Value="1">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Level}" Value="2">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Level}" Value="3">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Level}" Value="4">
                                            <Setter Property="Visibility" Value="Visible"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Rectangle.Style>
                        </Rectangle>
                        
                        <!-- 分类名称 -->
                        <TextBlock Text="{Binding CategoryName}" 
                                   VerticalAlignment="Center"
                                   FontSize="13"/>
                    </StackPanel>
                </DataTemplate>
            </ComboBox.ItemTemplate>
        </ComboBox>
    </Grid>
</UserControl>