using System.Windows;
using System.Windows.Controls;
using WPF_MVVM_Test.MVVM_Model.Bom;
using WPF_MVVM_Test.MVVM_ViewModel.Bom;

namespace WPF_MVVM_Test.MVVM_View.Bom
{
    /// <summary>
    /// 产品选择对话框
    /// </summary>
    public partial class ProductSelectDialog : System.Windows.Controls.UserControl
    {
        /// <summary>
        /// 选中的产品
        /// </summary>
        public ProductEntity? SelectedProduct { get; private set; }

        public ProductSelectDialog()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataContext is ProductSelectViewModel viewModel && viewModel.SelectedProduct != null)
            {
                SelectedProduct = viewModel.SelectedProduct;
                
                // 获取父窗口并设置DialogResult
                var parentWindow = Window.GetWindow(this);
                if (parentWindow != null)
                {
                    parentWindow.DialogResult = true;
                    parentWindow.Close();
                }
            }
            else
            {
                MessageBox.Show("请选择一个产品！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取父窗口并设置DialogResult
            var parentWindow = Window.GetWindow(this);
            if (parentWindow != null)
            {
                parentWindow.DialogResult = false;
                parentWindow.Close();
            }
        }
    }
}