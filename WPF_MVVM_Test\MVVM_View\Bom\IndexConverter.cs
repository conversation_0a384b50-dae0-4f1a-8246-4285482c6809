using System;
using System.Globalization;
using System.Windows.Controls;
using System.Windows.Data;

namespace WPF_MVVM_Test.MVVM_View.Bom
{
    /// <summary>
    /// 序号转换器
    /// </summary>
    public class IndexConverter : IValueConverter
    {
        public static readonly IndexConverter Instance = new IndexConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (parameter is DataGrid dataGrid && value != null)
            {
                var index = dataGrid.Items.IndexOf(value);
                return index + 1;
            }
            return 0;
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}