using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Bom
{
    /// <summary>
    /// BOM项模型（用于新增BOM）
    /// </summary>
    public class BomItemModel : INotifyPropertyChanged
    {
        private string _materialId = string.Empty;
        private string _parentItemId = string.Empty;
        private decimal _quantity;
        private decimal _lossRate;
        private int _inOutType = 1;
        private string _unit = string.Empty;
        private string _remark = string.Empty;

        /// <summary>
        /// 物料ID
        /// </summary>
        [JsonPropertyName("materialId")]
        public string MaterialId
        {
            get => _materialId;
            set
            {
                if (_materialId != value)
                {
                    _materialId = value;
                    OnPropertyChanged(nameof(MaterialId));
                }
            }
        }

        /// <summary>
        /// 父项ID
        /// </summary>
        [JsonPropertyName("parentItemId")]
        public string ParentItemId
        {
            get => _parentItemId;
            set
            {
                if (_parentItemId != value)
                {
                    _parentItemId = value;
                    OnPropertyChanged(nameof(ParentItemId));
                }
            }
        }

        /// <summary>
        /// 数量
        /// </summary>
        [JsonPropertyName("quantity")]
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged(nameof(Quantity));
                }
            }
        }

        /// <summary>
        /// 损耗率
        /// </summary>
        [JsonPropertyName("lossRate")]
        public decimal LossRate
        {
            get => _lossRate;
            set
            {
                if (_lossRate != value)
                {
                    _lossRate = value;
                    OnPropertyChanged(nameof(LossRate));
                }
            }
        }

        /// <summary>
        /// 进出类型
        /// </summary>
        [JsonPropertyName("inOutType")]
        public int InOutType
        {
            get => _inOutType;
            set
            {
                if (_inOutType != value)
                {
                    _inOutType = value;
                    OnPropertyChanged(nameof(InOutType));
                }
            }
        }

        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark
        {
            get => _remark;
            set
            {
                if (_remark != value)
                {
                    _remark = value;
                    OnPropertyChanged(nameof(Remark));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 新增BOM请求模型
    /// </summary>
    public class CreateBomRequest
    {
        [JsonPropertyName("bomNumber")]
        public string BomNumber { get; set; } = string.Empty;

        [JsonPropertyName("isSystemNumber")]
        public bool IsSystemNumber { get; set; }

        [JsonPropertyName("isDefault")]
        public bool IsDefault { get; set; }

        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;

        [JsonPropertyName("productId")]
        public string ProductId { get; set; } = string.Empty;

        [JsonPropertyName("productName")]
        public string ProductName { get; set; } = string.Empty;

        [JsonPropertyName("colorCode")]
        public string ColorCode { get; set; } = string.Empty;

        [JsonPropertyName("unit")]
        public string Unit { get; set; } = string.Empty;

        [JsonPropertyName("dailyOutput")]
        public decimal DailyOutput { get; set; }

        [JsonPropertyName("remark")]
        public string Remark { get; set; } = string.Empty;

        [JsonPropertyName("processRouteId")]
        public string ProcessRouteId { get; set; } = string.Empty;

        [JsonPropertyName("bomItems")]
        public List<BomItemModel> BomItems { get; set; } = new List<BomItemModel>();
    }

    /// <summary>
    /// 物料选择项模型（用于物料选择对话框）
    /// </summary>
    public class MaterialSelectionItem : INotifyPropertyChanged
    {
        private bool _isSelected;
        private string _id = string.Empty;
        private string _materialNumber = string.Empty;
        private string _materialName = string.Empty;
        private string _specificationModel = string.Empty;
        private string _unit = string.Empty;
        private string _materialType = string.Empty;
        private string _materialProperty = string.Empty;
        private decimal _quantity = 1;
        private decimal _lossRate = 0;
        private string _remark = string.Empty;

        /// <summary>
        /// 是否选中
        /// </summary>
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        /// <summary>
        /// 物料ID
        /// </summary>
        public string Id
        {
            get => _id;
            set
            {
                if (_id != value)
                {
                    _id = value;
                    OnPropertyChanged(nameof(Id));
                }
            }
        }

        /// <summary>
        /// 物料编号
        /// </summary>
        public string MaterialNumber
        {
            get => _materialNumber;
            set
            {
                if (_materialNumber != value)
                {
                    _materialNumber = value;
                    OnPropertyChanged(nameof(MaterialNumber));
                }
            }
        }

        /// <summary>
        /// 物料名称
        /// </summary>
        public string MaterialName
        {
            get => _materialName;
            set
            {
                if (_materialName != value)
                {
                    _materialName = value;
                    OnPropertyChanged(nameof(MaterialName));
                }
            }
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        public string SpecificationModel
        {
            get => _specificationModel;
            set
            {
                if (_specificationModel != value)
                {
                    _specificationModel = value;
                    OnPropertyChanged(nameof(SpecificationModel));
                }
            }
        }

        /// <summary>
        /// 单位
        /// </summary>
        public string Unit
        {
            get => _unit;
            set
            {
                if (_unit != value)
                {
                    _unit = value;
                    OnPropertyChanged(nameof(Unit));
                }
            }
        }

        /// <summary>
        /// 物料类型
        /// </summary>
        public string MaterialType
        {
            get => _materialType;
            set
            {
                if (_materialType != value)
                {
                    _materialType = value;
                    OnPropertyChanged(nameof(MaterialType));
                }
            }
        }

        /// <summary>
        /// 物料属性
        /// </summary>
        public string MaterialProperty
        {
            get => _materialProperty;
            set
            {
                if (_materialProperty != value)
                {
                    _materialProperty = value;
                    OnPropertyChanged(nameof(MaterialProperty));
                }
            }
        }

        /// <summary>
        /// 数量
        /// </summary>
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                if (_quantity != value)
                {
                    _quantity = value;
                    OnPropertyChanged(nameof(Quantity));
                }
            }
        }

        /// <summary>
        /// 损耗率
        /// </summary>
        public decimal LossRate
        {
            get => _lossRate;
            set
            {
                if (_lossRate != value)
                {
                    _lossRate = value;
                    OnPropertyChanged(nameof(LossRate));
                }
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remark
        {
            get => _remark;
            set
            {
                if (_remark != value)
                {
                    _remark = value;
                    OnPropertyChanged(nameof(Remark));
                }
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}