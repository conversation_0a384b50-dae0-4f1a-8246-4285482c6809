using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Plan
{
    /// <summary>
    /// 销售订单模型 - 根据后端API返回数据结构定义
    /// </summary>
    public class SalesOrderModel : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private string _salesCode = string.Empty;
        private string _salesName = string.Empty;
        private string _salesperson = string.Empty;
        private string _customerName = string.Empty;
        private string _remarks = string.Empty;
        private int _index;

        /// <summary>
        /// 销售订单ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id
        {
            get => _id;
            set => SetProperty(ref _id, value ?? string.Empty);
        }

        /// <summary>
        /// 销售编号
        /// </summary>
        [JsonPropertyName("salesCode")]
        public string SalesCode
        {
            get => _salesCode;
            set => SetProperty(ref _salesCode, value ?? string.Empty);
        }

        /// <summary>
        /// 销售名称
        /// </summary>
        [JsonPropertyName("salesName")]
        public string SalesName
        {
            get => _salesName;
            set => SetProperty(ref _salesName, value ?? string.Empty);
        }

        /// <summary>
        /// 销售人员
        /// </summary>
        [JsonPropertyName("salesperson")]
        public string Salesperson
        {
            get => _salesperson;
            set => SetProperty(ref _salesperson, value ?? string.Empty);
        }

        /// <summary>
        /// 客户名称
        /// </summary>
        [JsonPropertyName("customerName")]
        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value ?? string.Empty);
        }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remarks")]
        public string Remarks
        {
            get => _remarks;
            set => SetProperty(ref _remarks, value ?? string.Empty);
        }

        /// <summary>
        /// 序号 - 用于界面显示
        /// </summary>
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        #region INotifyPropertyChanged实现
        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
        #endregion
    }

    /// <summary>
    /// 销售订单API响应包装类
    /// </summary>
    public class SalesOrderApiResponse
    {
        [JsonPropertyName("data")]
        public SalesOrderData? Data { get; set; }

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 销售订单数据包装类
    /// </summary>
    public class SalesOrderData
    {
        [JsonPropertyName("totalCount")]
        public int TotalCount { get; set; }

        [JsonPropertyName("totalPage")]
        public int TotalPage { get; set; }

        [JsonPropertyName("data")]
        public List<SalesOrderModel> Data { get; set; } = new List<SalesOrderModel>();
    }
}