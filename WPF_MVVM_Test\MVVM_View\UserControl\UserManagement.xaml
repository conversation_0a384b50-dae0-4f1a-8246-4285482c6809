<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.UserManagement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border Background="White"
            CornerRadius="5"
            Padding="20"
            Margin="0,0,0,20">
        <StackPanel>
            <!-- 页面标题和工具栏 -->
            <Grid Margin="0,0,0,20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0"
                            Orientation="Horizontal">
                    <TextBlock Text="👥 用户管理"
                               FontSize="20"
                               FontWeight="Bold"
                               VerticalAlignment="Center"/>
                    <TextBlock Text="管理系统用户信息"
                               FontSize="14"
                               Foreground="Gray"
                               VerticalAlignment="Center"
                               Margin="15,0,0,0"/>
                </StackPanel>

                <StackPanel Grid.Column="1"
                            Orientation="Horizontal">
                    <Button Content="➕ 添加用户"
                            Background="#4CAF50"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Margin="0,0,10,0"
                            Cursor="Hand"/>
                    <Button Content="🔄 刷新"
                            Background="#2196F3"
                            Foreground="White"
                            BorderThickness="0"
                            Padding="15,8"
                            Cursor="Hand"/>
                </StackPanel>
            </Grid>

            <!-- 用户列表 -->
            <DataGrid AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      Background="White"
                      RowBackground="White"
                      AlternatingRowBackground="#F8F9FA"
                      BorderThickness="1"
                      BorderBrush="#E0E0E0"
                      Height="400"
                      ItemsSource="{Binding Users}">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="用户ID"
                                        Width="120"
                                        Binding="{Binding Id}"/>
                    <DataGridTextColumn Header="用户名"
                                        Width="120"
                                        Binding="{Binding UserName}"/>
                    <DataGridTextColumn Header="邮箱"
                                        Width="200"
                                        Binding="{Binding UserEmail}"/>
                    <DataGridTextColumn Header="手机号"
                                        Width="150"
                                        Binding="{Binding UserPhone}"/>
                    <DataGridTextColumn Header="性别"
                                        Width="80"
                                        Binding="{Binding UserSexText}"/>
                    <DataGridTextColumn Header="角色"
                                        Width="*"
                                        Binding="{Binding RoleNameText}"/>
                    <DataGridTemplateColumn Header="操作" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="编辑"
                                            Background="#FF9800"
                                            Foreground="White"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="2"
                                            Cursor="Hand"/>
                                    <Button Content="删除"
                                            Background="#F44336"
                                            Foreground="White"
                                            BorderThickness="0"
                                            Padding="8,4"
                                            Margin="2"
                                            Cursor="Hand"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>

            <!-- 测试数据按钮 -->
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Center"
                        Margin="0,20,0,0">
                <Button Content="📝 填充测试数据"
                        Background="#9C27B0"
                        Foreground="White"
                        BorderThickness="0"
                        Padding="15,8"
                        Margin="5"
                        Command="{Binding RefreshUsersCommand}"
                        Cursor="Hand"/>
            </StackPanel>
        </StackPanel>
    </Border>
</UserControl>
