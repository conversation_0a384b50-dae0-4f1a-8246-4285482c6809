﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WPF_MVVM_Test.MVVM_Model.Plan
{
    public class AddProductplanModel
    {
        public Guid Id { get; set; } = Guid.NewGuid();
        public string PlanNumber { get; set; } = string.Empty;
        public string PlanName { get; set; } = string.Empty;
        public Guid BomId { get; set; }
        public string SourceType { get; set; } = string.Empty;
        public string OrderNumber { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ProductNumber { get; set; } = string.Empty;
        public string ProductType { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public decimal PlanQuantity { get; set; }
        public DateTime PlanStartTime { get; set; }
        public DateTime PlanEndTime { get; set; }
        public DateTime RequiredDate { get; set; }
        public string? Remark { get; set; }
        public int Status { get; set; }
        public string? Attachment { get; set; }
    }
}
