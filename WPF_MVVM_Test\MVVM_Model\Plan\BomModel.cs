using System;
using System.Collections.Generic;

namespace WPF_MVVM_Test.MVVM_Model.Plan
{
    /// <summary>
    /// BOM模型
    /// </summary>
    public class BomModel
    {
        public int Index { get; set; }
        public string Id { get; set; } = string.Empty; // 新增：后端主键
        public string BomCode { get; set; } = string.Empty; // 对应后端bomNumber
        public string Version { get; set; } = string.Empty;
        public string IsDefaultBom { get; set; } = string.Empty; // "是"/"否"，由isDefault转换
        public int Quantity { get; set; } // 对应dailyOutput，类型转换
        public bool IsSelected { get; set; }
        // 新增后端字段
        public bool IsSystemNumber { get; set; }
        public string ProductId { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ColorCode { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public decimal DailyOutput { get; set; }
        public string Remark { get; set; } = string.Empty;
        public string ProcessRouteId { get; set; } = string.Empty;
        public string ProcessRouteName { get; set; } = string.Empty;
        public DateTime? CreatedTime { get; set; }
        public DateTime? LastUpdatedTime { get; set; }
        public int ItemCount { get; set; }
    }

    /// <summary>
    /// 产品BOM信息
    /// </summary>
    public class ProductBomInfo
    {
        public string ProductName { get; set; } = string.Empty;
        public string ProductNumber { get; set; } = string.Empty;
        public string Specification { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
    }

     /// <summary>
    /// BOM树节点模型（用于树形结构展示）
    /// </summary>
    public class BomTreeNode
    {
        public string Id { get; set; }
        public string BomId { get; set; }
        public string MaterialId { get; set; }
        public string ParentItemId { get; set; }
        public double Quantity { get; set; }
        public string Unit { get; set; }
        public double LossRate { get; set; }
        public int InOutType { get; set; }
        public string Remark { get; set; }
        public string BomNumber { get; set; }
        public string BomVersion { get; set; }
        public string ProductName { get; set; }
        public string ProductNumber { get; set; }
        public string ProductSpecification { get; set; }
        public int ProductType { get; set; }
        public string MaterialName { get; set; }
        public string MaterialNumber { get; set; }
        public string MaterialSpecification { get; set; }
        public int MaterialType { get; set; }
        public int Sequence { get; set; }
        public string DisplayName { get; set; }
        public string DisplayProductNumber { get; set; }
        public string DisplaySpecification { get; set; }
        public double UsageQuantity { get; set; }
        public string UsageRatio { get; set; }
        public bool IsExpandable { get; set; }
        public int Level { get; set; }
        public List<BomTreeNode> Children { get; set; } = new List<BomTreeNode>();
    }
}