using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model.Bom
{
    /// <summary>
    /// BOM新增请求模型
    /// </summary>
    public class BomAddRequestModel
    {
        /// <summary>
        /// BOM编号
        /// </summary>
        [JsonPropertyName("bomNumber")]
        public string BomNumber { get; set; } = string.Empty;

        /// <summary>
        /// 是否系统编号
        /// </summary>
        [JsonPropertyName("isSystemNumber")]
        public bool IsSystemNumber { get; set; } = true;

        /// <summary>
        /// 是否默认BOM
        /// </summary>
        [JsonPropertyName("isDefault")]
        public bool IsDefault { get; set; } = true;

        /// <summary>
        /// 版本
        /// </summary>
        [JsonPropertyName("version")]
        public string Version { get; set; } = string.Empty;

        /// <summary>
        /// 产品ID
        /// </summary>
        [JsonPropertyName("productId")]
        public string ProductId { get; set; } = string.Empty;

        /// <summary>
        /// 产品名称
        /// </summary>
        [JsonPropertyName("productName")]
        public string ProductName { get; set; } = string.Empty;

        /// <summary>
        /// 颜色代码
        /// </summary>
        [JsonPropertyName("colorCode")]
        public string ColorCode { get; set; } = string.Empty;

        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 日产量
        /// </summary>
        [JsonPropertyName("dailyOutput")]
        public double DailyOutput { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; } = string.Empty;

        /// <summary>
        /// 工艺路线ID
        /// </summary>
        [JsonPropertyName("processRouteId")]
        public string ProcessRouteId { get; set; } = string.Empty;

        /// <summary>
        /// BOM项目列表
        /// </summary>
        [JsonPropertyName("bomItems")]
        public List<BomAddItemModel> BomItems { get; set; } = new List<BomAddItemModel>();
    }

    /// <summary>
    /// BOM项目新增模型
    /// </summary>
    public class BomAddItemModel
    {
        /// <summary>
        /// 物料ID
        /// </summary>
        [JsonPropertyName("materialId")]
        public string MaterialId { get; set; } = string.Empty;

        /// <summary>
        /// 父项目ID
        /// </summary>
        [JsonPropertyName("parentItemId")]
        public string ParentItemId { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        [JsonPropertyName("quantity")]
        public double Quantity { get; set; }

        /// <summary>
        /// 损耗率
        /// </summary>
        [JsonPropertyName("lossRate")]
        public double LossRate { get; set; }

        /// <summary>
        /// 进出类型
        /// </summary>
        [JsonPropertyName("inOutType")]
        public int InOutType { get; set; } = 1;

        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark { get; set; } = string.Empty;
    }

    /// <summary>
    /// BOM新增响应模型
    /// </summary>
    public class BomAddResponseModel
    {
        /// <summary>
        /// 是否成功
        /// </summary>
        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        /// <summary>
        /// 状态码
        /// </summary>
        [JsonPropertyName("code")]
        public int Code { get; set; }

        /// <summary>
        /// 消息
        /// </summary>
        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }
}