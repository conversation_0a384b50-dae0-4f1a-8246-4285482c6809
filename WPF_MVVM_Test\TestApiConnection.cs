using System;
using System.Net.Http;
using System.Threading.Tasks;
using WPF_MVVM_Test.Services;

namespace WPF_MVVM_Test
{
    /// <summary>
    /// 测试API连接的辅助类
    /// </summary>
    public static class TestApiConnection
    {
        /// <summary>
        /// 测试基础HTTP连接
        /// </summary>
        public static async Task<bool> TestBasicConnectionAsync()
        {
            try
            {
                using var client = new HttpClient();
                client.BaseAddress = new Uri("http://localhost:64922/");
                client.Timeout = TimeSpan.FromSeconds(10);

                Console.WriteLine("正在测试基础连接...");
                var response = await client.GetAsync("api/WorkOrderTask/debug/simple-page?pageIndex=1&pageSize=5");
                
                Console.WriteLine($"响应状态码: {response.StatusCode}");
                Console.WriteLine($"响应是否成功: {response.IsSuccessStatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"响应内容长度: {content.Length}");
                    Console.WriteLine($"响应内容前200字符: {(content.Length > 200 ? content.Substring(0, 200) + "..." : content)}");
                    return true;
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"错误响应: {errorContent}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"连接测试失败: {ex.Message}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        /// <summary>
        /// 测试WorkOrderTaskApiService
        /// </summary>
        public static async Task<bool> TestApiServiceAsync()
        {
            try
            {
                Console.WriteLine("正在测试WorkOrderTaskApiService...");
                using var apiService = new WorkOrderTaskApiService();
                
                var response = await apiService.GetWorkOrderTaskPageAsync(1, 5);
                
                Console.WriteLine($"API服务响应成功: {response.IsSuc}");
                Console.WriteLine($"响应代码: {response.Code}");
                Console.WriteLine($"响应消息: {response.Msg}");
                
                if (response.IsSuc && response.Data != null)
                {
                    Console.WriteLine($"返回数据总数: {response.Data.TotalCount}");
                    Console.WriteLine($"当前页数据数量: {response.Data.Data?.Count ?? 0}");
                    return true;
                }
                else
                {
                    Console.WriteLine("API服务调用失败或返回空数据");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"API服务测试失败: {ex.Message}");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"内部异常: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        /// <summary>
        /// 运行所有测试
        /// </summary>
        public static async Task RunAllTestsAsync()
        {
            Console.WriteLine("=== 开始API连接测试 ===");
            Console.WriteLine();

            Console.WriteLine("1. 测试基础HTTP连接:");
            var basicTest = await TestBasicConnectionAsync();
            Console.WriteLine($"基础连接测试结果: {(basicTest ? "成功" : "失败")}");
            Console.WriteLine();

            Console.WriteLine("2. 测试API服务:");
            var apiTest = await TestApiServiceAsync();
            Console.WriteLine($"API服务测试结果: {(apiTest ? "成功" : "失败")}");
            Console.WriteLine();

            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine($"总体结果: {(basicTest && apiTest ? "所有测试通过" : "存在测试失败")}");
        }
    }
}
