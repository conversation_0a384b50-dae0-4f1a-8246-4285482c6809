using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web;
using WPF_MVVM_Test.MVVM_Model.Common;
using WPF_MVVM_Test.MVVM_Model.ProductionOrder;

namespace WPF_MVVM_Test.MVVM_Services
{
    public class ProductionOrderService
    {
        private readonly HttpService _httpService;

        public ProductionOrderService()
        {
            _httpService = new HttpService();
        }

        /// <summary>
        /// 获取生产工单列表
        /// </summary>
        /// <param name="orderNumber">工单编号</param>
        /// <param name="productName">产品名称</param>
        /// <param name="status">状态</param>
        /// <param name="pageIndex">页码</param>
        /// <param name="pageSize">页大小</param>
        /// <returns></returns>
        public async Task<ApiResponse<PagedData<ProductionOrderModel>>> GetProductionOrderListAsync(
            string orderNumber = null,
            string productName = null,
            string status = null,
            int pageIndex = 1,
            int pageSize = 10)
        {
            try
            {
                var queryParams = new List<string>();

                if (!string.IsNullOrEmpty(orderNumber))
                    queryParams.Add($"OrderNumber={HttpUtility.UrlEncode(orderNumber)}");

                if (!string.IsNullOrEmpty(productName))
                    queryParams.Add($"ProductName={HttpUtility.UrlEncode(productName)}");

                if (!string.IsNullOrEmpty(status))
                    queryParams.Add($"Status={HttpUtility.UrlEncode(status)}");

                queryParams.Add($"PageIndex={pageIndex}");
                queryParams.Add($"PageSize={pageSize}");

                var queryString = string.Join("&", queryParams);
                var url = $"api/ProductionOrder/GetProductionOrderList?{queryString}";

                var response = await _httpService.GetAsync<PagedData<ProductionOrderModel>>(url);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<PagedData<ProductionOrderModel>>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"获取生产工单列表失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 开始生产工单
        /// </summary>
        /// <param name="orderId">工单ID</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> StartProductionOrderAsync(string orderId)
        {
            try
            {
                var data = new { OrderId = orderId };
                var response = await _httpService.PostAsync<object>("api/ProductionOrder/StartProductionOrder", data);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"开始生产工单失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 取消生产工单
        /// </summary>
        /// <param name="orderId">工单ID</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> CancelProductionOrderAsync(string orderId)
        {
            try
            {
                var data = new { OrderId = orderId };
                var response = await _httpService.PostAsync<object>("api/ProductionOrder/CancelProductionOrder", data);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"取消生产工单失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 结束生产工单
        /// </summary>
        /// <param name="orderId">工单ID</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> EndProductionOrderAsync(string orderId)
        {
            try
            {
                var data = new { OrderId = orderId };
                var response = await _httpService.PostAsync<object>("api/ProductionOrder/EndProductionOrder", data);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"结束生产工单失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 撤回生产工单
        /// </summary>
        /// <param name="orderId">工单ID</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> RecallProductionOrderAsync(string orderId)
        {
            try
            {
                var data = new { OrderId = orderId };
                var response = await _httpService.PostAsync<object>("api/ProductionOrder/RecallProductionOrder", data);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"撤回生产工单失败: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// 排产生产工单
        /// </summary>
        /// <param name="request">排产请求</param>
        /// <returns></returns>
        public async Task<ApiResponse<object>> ScheduleProductionAsync(object request)
        {
            try
            {
                var response = await _httpService.PostAsync<object>("api/ProductionOrder/ScheduleProduction", request);
                return response;
            }
            catch (Exception ex)
            {
                return new ApiResponse<object>
                {
                    IsSuc = false,
                    Code = 500,
                    Msg = $"排产生产工单失败: {ex.Message}"
                };
            }
        }

        public void Dispose()
        {
            _httpService?.Dispose();
        }
    }
} 