﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace WpfTest
{
    /// <summary>
    /// Window1.xaml 的交互逻辑
    /// </summary>
    public partial class Window1 : Window,INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        private string  name { get; set; }
        private string  pwd { get; set; }
        public Window1()
        {
            InitializeComponent();
        }

    }
}
