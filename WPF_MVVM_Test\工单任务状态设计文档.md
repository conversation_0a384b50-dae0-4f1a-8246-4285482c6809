# 工单任务状态设计文档

## 当前状态定义

### 1. 状态枚举
系统中定义了以下6种工单任务状态：

| 状态代码 | 状态名称 | 颜色代码 | 颜色显示 | 描述 |
|---------|---------|---------|---------|------|
| `未派工` | 未派工 | `#8C8C8C` | 灰色 | 任务刚创建，尚未分配给工作人员 |
| `已下达` | 已下达 | `#1890FF` | 蓝色 | 任务已分配，等待开工 |
| `进行中` | 进行中 | `#52C41A` | 绿色 | 任务正在执行中 |
| `已完成` | 已完成 | `#FA8C16` | 橙色 | 任务执行完成，等待报工 |
| `已暂停` | 已暂停 | `#FF4D4F` | 红色 | 任务暂停执行 |
| `已关闭` | 已关闭 | `#6C757D` | 深灰色 | 任务关闭，不再执行 |

## 状态流转逻辑

### 2. 状态转换图
```
未派工 → 已下达 → 进行中 → 已完成
  ↓        ↓        ↓        ↓
已关闭 ← 已关闭 ← 已关闭   已关闭
  ↑                 ↓
  ← ← ← ← ← ← ← 已暂停
```

### 3. 状态转换规则
- **未派工** → **已下达**：通过"派工"操作
- **已下达** → **进行中**：通过"开工"操作  
- **进行中** → **已完成**：通过"完工"操作
- **已完成** → **未派工**：通过"重启"操作
- **任何状态** → **已关闭**：通过"关闭"操作
- **已关闭** → **未派工**：通过"重启"操作
- **进行中** → **已暂停**：通过系统异常或手动暂停

## 按钮显示逻辑

### 4. 各状态对应的操作按钮

#### 未派工状态
- **派工按钮** (`StartButtonStyle`) - 将状态改为"已下达"
- **编辑按钮** (`EditButtonStyle`) - 编辑任务信息
- **查看按钮** (`ViewButtonStyle`) - 查看任务详情

#### 已下达状态  
- **开工按钮** (`WorkButtonStyle`) - 将状态改为"进行中"
- **关闭按钮** (`CloseButtonStyle`) - 将状态改为"已关闭"
- **查看按钮** (`ViewButtonStyle`) - 查看任务详情

#### 进行中状态
- **完工按钮** (`CompleteButtonStyle`) - 将状态改为"已完成"
- **关闭按钮** (`CloseButtonStyle`) - 将状态改为"已关闭"  
- **查看按钮** (`ViewButtonStyle`) - 查看任务详情

#### 已完成状态
- **报工按钮** (`ReportButtonStyle`) - 打开报工对话框
- **重启按钮** (`RestartButtonStyle`) - 将状态改为"未派工"
- **查看按钮** (`ViewButtonStyle`) - 查看任务详情

#### 已关闭状态
- **重启按钮** (`RestartButtonStyle`) - 将状态改为"未派工"
- **查看按钮** (`ViewButtonStyle`) - 查看任务详情

#### 已暂停状态
- 当前未定义特定按钮，建议添加：
  - **恢复按钮** - 将状态改为"进行中"
  - **关闭按钮** - 将状态改为"已关闭"
  - **查看按钮** - 查看任务详情

## 数据库设计建议

### 5. 状态字段设计

#### 方案一：字符串状态（当前实现）
```sql
CREATE TABLE WorkOrderTask (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    TaskNumber NVARCHAR(50) NOT NULL,
    TaskName NVARCHAR(100) NOT NULL,
    Status NVARCHAR(20) NOT NULL DEFAULT '未派工',
    -- 其他字段...
    
    CONSTRAINT CK_WorkOrderTask_Status 
    CHECK (Status IN ('未派工', '已下达', '进行中', '已完成', '已暂停', '已关闭'))
);
```

#### 方案二：整数状态码（推荐）
```sql
CREATE TABLE WorkOrderTask (
    Id UNIQUEIDENTIFIER PRIMARY KEY,
    TaskNumber NVARCHAR(50) NOT NULL,
    TaskName NVARCHAR(100) NOT NULL,
    StatusCode INT NOT NULL DEFAULT 0,
    -- 其他字段...
    
    CONSTRAINT CK_WorkOrderTask_StatusCode 
    CHECK (StatusCode BETWEEN 0 AND 5)
);

-- 状态码映射
-- 0: 未派工
-- 1: 已下达  
-- 2: 进行中
-- 3: 已完成
-- 4: 已暂停
-- 5: 已关闭
```

### 6. 状态历史记录表
```sql
CREATE TABLE WorkOrderTaskStatusHistory (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TaskId UNIQUEIDENTIFIER NOT NULL,
    FromStatus NVARCHAR(20),
    ToStatus NVARCHAR(20) NOT NULL,
    ChangeTime DATETIME2 NOT NULL DEFAULT GETDATE(),
    ChangedBy NVARCHAR(50),
    Reason NVARCHAR(200),
    
    FOREIGN KEY (TaskId) REFERENCES WorkOrderTask(Id)
);
```

## 代码实现位置

### 7. 关键文件说明

#### 状态定义和逻辑
- `WorkOrderTaskModel.cs` - 状态属性定义和计算属性
- `WorkOrderTaskService.cs` - 状态更新方法
- `StatusToVisibilityConverter.cs` - 状态到按钮可见性转换

#### UI显示
- `WorkOrderTaskControl.xaml` - 按钮定义和可见性绑定
- 状态颜色样式定义

#### 业务逻辑
- `WorkOrderTaskViewModel.cs` - 状态转换命令实现

## 扩展建议

### 8. 功能增强
1. **添加已暂停状态的按钮支持**
2. **增加状态转换权限控制**
3. **添加状态变更日志记录**
4. **支持批量状态操作**
5. **添加状态转换确认对话框**

### 9. 测试数据建议
为了测试不同状态的按钮显示，建议在数据库中插入以下测试数据：

```sql
INSERT INTO WorkOrderTask (Id, TaskNumber, TaskName, Status, ...) VALUES
(NEWID(), 'WO001', '测试任务1', '未派工', ...),
(NEWID(), 'WO002', '测试任务2', '已下达', ...),
(NEWID(), 'WO003', '测试任务3', '进行中', ...),
(NEWID(), 'WO004', '测试任务4', '已完成', ...),
(NEWID(), 'WO005', '测试任务5', '已暂停', ...),
(NEWID(), 'WO006', '测试任务6', '已关闭', ...);
```

这样可以在界面上看到每种状态对应的不同按钮组合。
