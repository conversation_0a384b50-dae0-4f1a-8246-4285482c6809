<Window x:Class="WPF_MVVM_Test.MVVM_View.Bom.MaterialSelectionDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Bom"
        mc:Ignorable="d"
        Title="添加物料配件"
        Height="700"
        Width="1200"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        <local:RowIndexConverter x:Key="RowIndexConverter"/>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="添加物料配件"
                   FontSize="24"
                   FontWeight="Bold"
                   Margin="0,0,0,20"
                   HorizontalAlignment="Center"/>

        <!-- 选项卡 -->
        <TabControl Grid.Row="1" Margin="0,0,0,20">
            <TabItem Header="物料" IsSelected="True">
                <Grid Height="0"/> <!-- 空内容，只是为了显示选项卡 -->
            </TabItem>
            <TabItem Header="产品">
                <Grid Height="0"/> <!-- 空内容，只是为了显示选项卡 -->
            </TabItem>
        </TabControl>

        <!-- 搜索和筛选区域 -->
        <materialDesign:Card Grid.Row="2" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 搜索条件 -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 物料编号/名称搜索 -->
                    <TextBox Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="请输入物料编号/名称"
                             Text="{Binding SearchKeyword, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="50"/>

                    <!-- 单位筛选 -->
                    <ComboBox Grid.Column="1"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              materialDesign:HintAssist.Hint="请选择单位"
                              ItemsSource="{Binding UnitOptions}"
                              SelectedItem="{Binding SelectedUnit}"
                              Margin="0,0,10,0"
                              Height="50"/>

                    <!-- 类型筛选 -->
                    <ComboBox Grid.Column="2"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              materialDesign:HintAssist.Hint="请选择类型"
                              ItemsSource="{Binding TypeOptions}"
                              SelectedItem="{Binding SelectedType}"
                              Margin="0,0,10,0"
                              Height="50"/>

                    <!-- 属性筛选 -->
                    <ComboBox Grid.Column="3"
                              Style="{StaticResource MaterialDesignOutlinedComboBox}"
                              materialDesign:HintAssist.Hint="请选择物料属性"
                              ItemsSource="{Binding PropertyOptions}"
                              SelectedItem="{Binding SelectedProperty}"
                              Margin="0,0,10,0"
                              Height="50"/>

                    <!-- 重置按钮 -->
                    <Button Grid.Column="4"
                            Content="重置"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Command="{Binding ResetCommand}"
                            Width="80"
                            Height="40"
                            Margin="0,0,10,0"
                            VerticalAlignment="Center"/>

                    <!-- 查询按钮 -->
                    <Button Grid.Column="5"
                            Content="查询"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Command="{Binding SearchCommand}"
                            Width="80"
                            Height="40"
                            VerticalAlignment="Center"/>
                </Grid>

                <!-- 物料列表 -->
                <DataGrid Grid.Row="1"
                          ItemsSource="{Binding MaterialItems}"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          CanUserReorderColumns="False"
                          CanUserResizeRows="False"
                          CanUserSortColumns="True"
                          HeadersVisibility="Column"
                          GridLinesVisibility="All"
                          AlternatingRowBackground="#F8F8F8"
                          RowBackground="White"
                          BorderBrush="#E0E0E0"
                          BorderThickness="1"
                          FontSize="12"
                          SelectionMode="Extended">

                    <DataGrid.Columns>
                        <!-- 选择框 -->
                        <DataGridTemplateColumn Header="" Width="50">
                            <DataGridTemplateColumn.HeaderTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding DataContext.IsAllSelected, RelativeSource={RelativeSource AncestorType=Window}}"
                                              Command="{Binding DataContext.SelectAllCommand, RelativeSource={RelativeSource AncestorType=Window}}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.HeaderTemplate>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <CheckBox IsChecked="{Binding IsSelected, UpdateSourceTrigger=PropertyChanged}"
                                              HorizontalAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号" Width="60" IsReadOnly="True">
                            <DataGridTextColumn.Binding>
                                <Binding Path="." Converter="{StaticResource RowIndexConverter}"/>
                            </DataGridTextColumn.Binding>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 物料编号 -->
                        <DataGridTextColumn Header="物料编号" 
                                            Binding="{Binding MaterialNumber}" 
                                            Width="120"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 物料名称 -->
                        <DataGridTextColumn Header="物料名称" 
                                            Binding="{Binding MaterialName}" 
                                            Width="150"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 规格型号 -->
                        <DataGridTextColumn Header="规格型号" 
                                            Binding="{Binding SpecificationModel}" 
                                            Width="120"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 单位 -->
                        <DataGridTextColumn Header="单位" 
                                            Binding="{Binding Unit}" 
                                            Width="80"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 物料类型 -->
                        <DataGridTextColumn Header="物料类型" 
                                            Binding="{Binding MaterialType}" 
                                            Width="100"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 物料属性 -->
                        <DataGridTextColumn Header="物料属性" 
                                            Binding="{Binding MaterialProperty}" 
                                            Width="100"
                                            IsReadOnly="True">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 数量 -->
                        <DataGridTemplateColumn Header="数量" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBox Text="{Binding Quantity, UpdateSourceTrigger=PropertyChanged}"
                                             Style="{StaticResource MaterialDesignTextBox}"
                                             HorizontalAlignment="Stretch"
                                             VerticalAlignment="Center"
                                             TextAlignment="Center"
                                             Margin="5"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 损耗率 -->
                        <DataGridTemplateColumn Header="损耗率(%)" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBox Text="{Binding LossRate, UpdateSourceTrigger=PropertyChanged}"
                                             Style="{StaticResource MaterialDesignTextBox}"
                                             HorizontalAlignment="Stretch"
                                             VerticalAlignment="Center"
                                             TextAlignment="Center"
                                             Margin="5"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- 分页信息 -->
        <Grid Grid.Row="3" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <TextBlock Grid.Column="0"
                       Text="{Binding PageInfo}"
                       VerticalAlignment="Center"
                       FontSize="12"
                       Foreground="#666"/>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="首页"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding FirstPageCommand}"
                        Width="60"
                        Height="30"
                        Margin="0,0,5,0"/>
                
                <Button Content="上一页"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding PreviousPageCommand}"
                        Width="60"
                        Height="30"
                        Margin="0,0,5,0"/>
                
                <Button Content="下一页"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding NextPageCommand}"
                        Width="60"
                        Height="30"
                        Margin="0,0,5,0"/>
                
                <Button Content="末页"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding LastPageCommand}"
                        Width="60"
                        Height="30"/>
            </StackPanel>
        </Grid>

        <!-- 按钮区域 -->
        <StackPanel Grid.Row="4" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Right">
            <TextBlock Text="{Binding SelectedCountText}"
                       VerticalAlignment="Center"
                       Margin="0,0,20,0"
                       FontSize="12"
                       Foreground="#666"/>
            
            <Button Content="取消"
                    Style="{StaticResource MaterialDesignOutlinedButton}"
                    Width="100"
                    Height="40"
                    Margin="0,0,15,0"
                    Click="CancelButton_Click"/>
            
            <Button Content="确定"
                    Style="{StaticResource MaterialDesignRaisedButton}"
                    Width="100"
                    Height="40"
                    Click="ConfirmButton_Click"/>
        </StackPanel>
    </Grid>
</Window>