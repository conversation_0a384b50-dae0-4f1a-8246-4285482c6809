using System.Linq;
using System.Windows;
using WPF_MVVM_Test.MVVM_Model.Process;
using WPF_MVVM_Test.MVVM_ViewModel.Process_process;

namespace WPF_MVVM_Test.MVVM_View.Bom
{
    /// <summary>
    /// ProcessRouteSelectionDialog.xaml 的交互逻辑
    /// </summary>
    public partial class ProcessRouteSelectionDialog : Window
    {
        private ProcessRouteSelectionViewModel _viewModel;

        public ProcessRouteSelectionDialog()
        {
            InitializeComponent();
            _viewModel = new ProcessRouteSelectionViewModel();
            DataContext = _viewModel;
        }

        /// <summary>
        /// 选中的工艺路线
        /// </summary>
        public ProcessRoute? SelectedProcessRoute { get; private set; }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void ConfirmButton_Click(object sender, RoutedEventArgs e)
        {
            // 获取选中的工艺路线
            SelectedProcessRoute = _viewModel.ProcessRoutes.FirstOrDefault(x => x.IsSelected);

            if (SelectedProcessRoute == null)
            {
                MessageBox.Show("请选择一个工艺路线！", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            DialogResult = true;
            Close();
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}