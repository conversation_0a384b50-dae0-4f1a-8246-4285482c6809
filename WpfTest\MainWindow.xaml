﻿<Window x:Class="WpfTest.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfTest"
        mc:Ignorable="d"
        Title="登录页面"
        Height="450"
        Width="800" Background="Pink">


    <Grid>
        <!-- 用户名标签 -->
        <Label Content="用户名"
               HorizontalAlignment="Left"
               Margin="293,154,0,0"
               VerticalAlignment="Top"/>

        <!-- 用户名输入框：双向绑定到Username属性 -->
        <TextBox Text="{Binding Username}"
                 HorizontalAlignment="Left"
                 Margin="376,158,0,0"
                 TextWrapping="Wrap"
                 VerticalAlignment="Top"
                 Width="120"/>

        <!-- 密码标签 -->
        <Label Content="密码"
               HorizontalAlignment="Left"
               Margin="293,196,0,0"
               VerticalAlignment="Top"/>

        <!-- 密码输入框：双向绑定到Password属性 -->
        <TextBox Text="{Binding Password}"
                 HorizontalAlignment="Left"
                 Margin="376,200,0,0"
                 TextWrapping="Wrap"
                 VerticalAlignment="Top"
                 Width="120"/>

        <!-- 登录按钮 -->
        <Button x:Name="LoginButton"
                Content="登录"
                HorizontalAlignment="Left"
                Margin="376,259,0,0"
                VerticalAlignment="Top"
                Width="120"
                Click="LoginButton_Click"/>

        <!-- 测试按钮：演示PropertyChanged的作用 -->
        <Button Content="测试自动填充"
                HorizontalAlignment="Left"
                Margin="560,259,0,0"
                VerticalAlignment="Top"
                Width="120"
                Click="TestButton_Click"/>

    </Grid>
</Window>
