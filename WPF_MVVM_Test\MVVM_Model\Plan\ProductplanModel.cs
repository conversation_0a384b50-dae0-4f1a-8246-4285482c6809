﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using WPF_MVVM_Test.Services.Plan;

namespace WPF_MVVM_Test.MVVM_Model.Plan
{
    public class ProductplanModel:INotifyPropertyChanged
    {
        private Guid _id;
        private string _planNumber = string.Empty;
        private string _planName = string.Empty;
        private string _bomId = string.Empty;
        private string _sourceType = string.Empty;
        private string _orderNumber = string.Empty;
        private string _productName = string.Empty;
        private string _productNumber = string.Empty;
        private string _productType = string.Empty;
        private string _specification = string.Empty;
        private string _unit = string.Empty;
        private decimal _planQuantity;
        private DateTime? _planStartTime;
        private DateTime? _planEndTime;
        private DateTime? _requiredDate;
        private int _status;
        private string _remark = string.Empty;
        private string _attachment = string.Empty;
        private int _index; // 用于界面显示序号

        /// <summary>
        /// 生产计划ID
        /// </summary>
        [JsonPropertyName("id")]
        public Guid Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        /// <summary>
        /// 计划编号
        /// </summary>
        [JsonPropertyName("planNumber")]
        public string PlanNumber
        {
            get => _planNumber;
            set => SetProperty(ref _planNumber, value ?? string.Empty);
        }

        /// <summary>
        /// 计划名称
        /// </summary>
        [JsonPropertyName("planName")]
        public string PlanName
        {
            get => _planName;
            set => SetProperty(ref _planName, value ?? string.Empty);
        }

        /// <summary>
        /// BOM ID
        /// </summary>
        [JsonPropertyName("bomId")]
        public string BomId
        {
            get => _bomId;
            set => SetProperty(ref _bomId, value ?? string.Empty);
        }

        /// <summary>
        /// 来源类型
        /// </summary>
        [JsonPropertyName("sourceType")]
        public string SourceType
        {
            get => _sourceType;
            set => SetProperty(ref _sourceType, value ?? string.Empty);
        }

        /// <summary>
        /// 订单编号
        /// </summary>
        [JsonPropertyName("orderNumber")]
        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value ?? string.Empty);
        }

        /// <summary>
        /// 产品名称
        /// </summary>
        [JsonPropertyName("productName")]
        public string ProductName
        {
            get => _productName;
            set => SetProperty(ref _productName, value ?? string.Empty);
        }

        /// <summary>
        /// 产品编号
        /// </summary>
        [JsonPropertyName("productNumber")]
        public string ProductNumber
        {
            get => _productNumber;
            set => SetProperty(ref _productNumber, value ?? string.Empty);
        }

        /// <summary>
        /// 产品类型
        /// </summary>
        [JsonPropertyName("productType")]
        public string ProductType
        {
            get => _productType;
            set => SetProperty(ref _productType, value ?? string.Empty);
        }

        /// <summary>
        /// 规格型号
        /// </summary>
        [JsonPropertyName("specification")]
        public string Specification
        {
            get => _specification;
            set => SetProperty(ref _specification, value ?? string.Empty);
        }

        /// <summary>
        /// 单位
        /// </summary>
        [JsonPropertyName("unit")]
        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value ?? string.Empty);
        }

        /// <summary>
        /// 计划数量 - decimal类型支持小数
        /// </summary>
        [JsonPropertyName("planQuantity")]
        [JsonConverter(typeof(StringToDecimalConverter))]
        public decimal PlanQuantity
        {
            get => _planQuantity;
            set => SetProperty(ref _planQuantity, value);
        }

        /// <summary>
        /// 计划开工时间
        /// </summary>
        [JsonPropertyName("planStartTime")]
        public DateTime? PlanStartTime
        {
            get => _planStartTime;
            set => SetProperty(ref _planStartTime, value);
        }

        /// <summary>
        /// 计划完工时间
        /// </summary>
        [JsonPropertyName("planEndTime")]
        public DateTime? PlanEndTime
        {
            get => _planEndTime;
            set => SetProperty(ref _planEndTime, value);
        }

        /// <summary>
        /// 要求完工时间
        /// </summary>
        [JsonPropertyName("requiredDate")]
        public DateTime? RequiredDate
        {
            get => _requiredDate;
            set => SetProperty(ref _requiredDate, value);
        }

        /// <summary>
        /// 状态 (未分解0 已分解1 已完成2 已关闭3 已撤回4 进行中5)
        /// </summary>
        [JsonPropertyName("status")]
        public int Status
        {
            get => _status;
            set
            {
                SetProperty(ref _status, value);
                OnPropertyChanged(nameof(StatusText));
                OnPropertyChanged(nameof(StatusColor));
            }
        }

        /// <summary>
        /// 状态文本显示
        /// </summary>
        public string StatusText
        {
            get
            {
                return Status switch
                {
                    0 => "未分解",
                    1 => "已分解",
                    2 => "已完成",
                    3 => "已关闭",
                    4 => "已撤回",
                    5 => "进行中",
                    _ => "未知"
                };
            }
        }

        /// <summary>
        /// 状态颜色
        /// </summary>
        public string StatusColor
        {
            get
            {
                return Status switch
                {
                    0 => "#8C8C8C",  // 未分解 - 灰色
                    1 => "#1890FF",  // 已分解 - 蓝色
                    2 => "#52C41A",  // 已完成 - 绿色
                    3 => "#FA8C16",  // 已关闭 - 橙色
                    4 => "#FF4D4F",  // 已撤回 - 红色
                    5 => "#FFB6C1",  // 进行中 - 粉色
                    _ => "#8C8C8C"   // 未知 - 灰色
                };
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        [JsonPropertyName("remark")]
        public string Remark
        {
            get => _remark;
            set => SetProperty(ref _remark, value ?? string.Empty);
        }

        /// <summary>
        /// 附件
        /// </summary>
        [JsonPropertyName("attachment")]
        public string Attachment
        {
            get => _attachment;
            set => SetProperty(ref _attachment, value ?? string.Empty);
        }

        /// <summary>
        /// 序号 - 用于界面显示
        /// </summary>
        public int Index
        {
            get => _index;
            set => SetProperty(ref _index, value);
        }

        /// <summary>
        /// 工序数量 - 兼容原有界面字段
        /// </summary>
        public int ProcessCount => 0; // 如果后端没有此字段，可以设为默认值或计算值

        /// <summary>
        /// 成品名称 - 兼容原有界面字段，映射到ProductName
        /// </summary>
        public string PlanCode => PlanNumber; // 兼容原有界面的PlanCode字段

        /// <summary>
        /// 成品编号 - 兼容原有界面字段，映射到ProductNumber
        /// </summary>
        public string ProductCode => ProductNumber;

        /// <summary>
        /// 要求完工时间 - 兼容原有界面字段
        /// </summary>
        public DateTime? RequiredEndTime => RequiredDate;

        /// <summary>
        /// 属性变更通知事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 设置属性值并触发通知
        /// </summary>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public ProductplanModel()
        {
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        public ProductplanModel(string planNumber, string planName, string productName)
        {
            PlanNumber = planNumber;
            PlanName = planName;
            ProductName = productName;
        }
    }

    /// <summary>
    /// API响应包装类 - 根据后端返回的数据结构
    /// </summary>
    public class ProductPlanApiResponse
    {
        [JsonPropertyName("data")]
        public ProductPlanData? Data { get; set; }

        [JsonPropertyName("isSuc")]
        public bool IsSuc { get; set; }

        [JsonPropertyName("code")]
        public int Code { get; set; }

        [JsonPropertyName("msg")]
        public string Msg { get; set; } = string.Empty;
    }

    /// <summary>
    /// 生产计划数据包装类
    /// </summary>
    public class ProductPlanData
    {
        [JsonPropertyName("totalCount")]
        public int TotalCount { get; set; }

        [JsonPropertyName("totalPage")]
        public int TotalPage { get; set; }

        [JsonPropertyName("data")]
        public List<ProductplanModel> Data { get; set; } = new List<ProductplanModel>();
    }
}
