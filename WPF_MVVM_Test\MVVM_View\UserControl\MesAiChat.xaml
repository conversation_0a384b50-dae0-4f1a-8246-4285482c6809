<UserControl x:Class="WPF_MVVM_Test.MVVM_View.UserControl.MesAiChat"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="800">

    <UserControl.Resources>
        <!-- 布尔值到背景色转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

        <!-- 用户消息和AI消息的背景色转换器 -->
        <Style x:Key="UserMessageStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="#007ACC"/>
            <Setter Property="HorizontalAlignment"
                    Value="Right"/>
        </Style>

        <Style x:Key="AiMessageStyle"
               TargetType="Border">
            <Setter Property="Background"
                    Value="#F5F5F5"/>
            <Setter Property="HorizontalAlignment"
                    Value="Left"/>
        </Style>
    </UserControl.Resources>

    <!-- 使用DockPanel布局，确保输入框固定在底部 -->
    <DockPanel LastChildFill="True">
        <!-- 服务状态栏 - 固定在顶部 -->
        <Border DockPanel.Dock="Top"
                Background="#F0F0F0"
                BorderThickness="0,0,0,1"
                BorderBrush="#DDDDDD"
                Padding="10,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="服务状态: "
                           FontSize="12"
                           Foreground="#666666"/>
                <TextBlock Text="{Binding ServiceStatus}"
                           FontSize="12"
                           FontWeight="Bold"
                           Margin="0,0,10,0">
                    <TextBlock.Style>
                        <Style TargetType="TextBlock">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsServiceHealthy}"
                                             Value="True">
                                    <Setter Property="Foreground"
                                            Value="Green"/>
                                </DataTrigger>
                                <DataTrigger Binding="{Binding IsServiceHealthy}"
                                             Value="False">
                                    <Setter Property="Foreground"
                                            Value="Red"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </TextBlock.Style>
                </TextBlock>

                <!-- 刷新按钮 -->
                <Button Command="{Binding RefreshServiceCommand}"
                        Content="🔄 重新检查"
                        FontSize="10"
                        Padding="5,2"
                        Background="Transparent"
                        BorderThickness="1"
                        BorderBrush="#CCCCCC"
                        ToolTip="重新检查服务状态"
                        Margin="0,0,5,0"/>

                <!-- 测试连接按钮 -->
                <Button Command="{Binding TestConnectionCommand}"
                        Content="🔗 测试连接"
                        FontSize="10"
                        Padding="5,2"
                        Background="Transparent"
                        BorderThickness="1"
                        BorderBrush="#CCCCCC"
                        ToolTip="测试API连接"/>
            </StackPanel>
        </Border>

        <!-- 输入区域 - 固定在底部 -->
        <Border DockPanel.Dock="Bottom"
                Background="White"
                BorderThickness="0,1,0,0"
                BorderBrush="#DDDDDD">
            <Grid Margin="10,10,10,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 输入框 -->
                <TextBox Grid.Column="0"
                         x:Name="MessageTextBox"
                         Text="{Binding CurrentMessage, UpdateSourceTrigger=PropertyChanged}"
                         materialDesign:HintAssist.Hint="请输入您关于MES系统的问题..."
                         Height="40"
                         FontSize="14"
                         Margin="0,0,10,0"
                         VerticalContentAlignment="Center"
                         IsEnabled="{Binding IsServiceHealthy}"
                         KeyDown="MessageTextBox_KeyDown"/>

                <!-- 发送按钮 -->
                <Button Grid.Column="1"
                        Command="{Binding SendMessageCommand}"
                        Content="发送"
                        Height="40"
                        Width="80"
                        FontSize="14"
                        Background="#007ACC"
                        Foreground="White"
                        BorderThickness="0"/>
            </Grid>
        </Border>

        <!-- 聊天区域 - 填充剩余空间 -->
        <ScrollViewer x:Name="ChatScrollViewer"
                      VerticalScrollBarVisibility="Auto"
                      HorizontalScrollBarVisibility="Disabled"
                      Background="Transparent">
            <StackPanel Margin="0,0,0,0">
                <ItemsControl x:Name="ChatItemsControl"
                              ItemsSource="{Binding ChatMessages}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="10,5">
                                <Border Padding="15,10"
                                        MaxWidth="600">
                                    <Border.Style>
                                        <Style TargetType="Border">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding IsUser}"
                                                             Value="True">
                                                    <Setter Property="Background"
                                                            Value="#007ACC"/>
                                                    <Setter Property="HorizontalAlignment"
                                                            Value="Right"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding IsUser}"
                                                             Value="False">
                                                    <Setter Property="Background"
                                                            Value="#F5F5F5"/>
                                                    <Setter Property="HorizontalAlignment"
                                                            Value="Left"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Border.Style>
                                    <StackPanel>
                                        <TextBlock Text="{Binding Content}"
                                                   TextWrapping="Wrap"
                                                   FontSize="14">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding IsUser}"
                                                                     Value="True">
                                                            <Setter Property="Foreground"
                                                                    Value="White"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding IsUser}"
                                                                     Value="False">
                                                            <Setter Property="Foreground"
                                                                    Value="Black"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>

                                        <TextBlock Text="{Binding Timestamp, StringFormat='{}{0:HH:mm}'}"
                                                   FontSize="10"
                                                   Margin="0,5,0,0"
                                                   HorizontalAlignment="Right"
                                                   Foreground="#999999"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>

                <!-- 正在输入指示器 -->
                <Border Visibility="{Binding IsAiTyping, Converter={StaticResource BooleanToVisibilityConverter}}"
                        Margin="10,5,10,10"
                        Padding="15,10"
                        HorizontalAlignment="Left"
                        Background="#F5F5F5">
                    <TextBlock Text="MES智能助手正在思考中..."
                               FontSize="14"
                               Foreground="#666666"/>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </DockPanel>
</UserControl>
