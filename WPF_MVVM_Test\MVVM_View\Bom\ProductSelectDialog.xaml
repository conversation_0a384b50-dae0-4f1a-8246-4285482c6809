<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Bom.ProductSelectDialog"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.Bom"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Bom"
             MinHeight="400" 
             MinWidth="600"
             MaxHeight="800"
             MaxWidth="1200">
    
    <UserControl.DataContext>
        <vm:ProductSelectViewModel/>
    </UserControl.DataContext>
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,15" Padding="15">
            <StackPanel>
                <TextBlock Text="产品选择" 
                           FontSize="18"    
                           FontWeight="Bold" 
                           Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBox Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="输入产品编号/名称搜索"
                             Text="{Binding MaterialNumberFilter, UpdateSourceTrigger=PropertyChanged}"
                             x:Name="SearchTextBox"
                             Margin="0,0,10,0"/>
                    
                    <Button Grid.Column="1"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="搜索"
                            Command="{Binding SearchCommand}"/>
                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- 产品列表 -->
        <materialDesign:Card Grid.Row="1" Padding="15">
            <Grid>
                <!-- 加载指示器 -->
                <ProgressBar IsIndeterminate="True" 
                             VerticalAlignment="Top"
                             Height="4">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>

                <!-- 无数据提示 -->
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsLoading}" Value="False"/>
                                        <Condition Binding="{Binding ProductItems.Count}" Value="0"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Visibility" Value="Visible"/>
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>

                    <materialDesign:PackIcon Kind="PackageVariantClosed" 
                                             Width="64" 
                                             Height="64" 
                                             Foreground="#CCCCCC"
                                             Margin="0,0,0,20"/>
                    <TextBlock Text="暂无产品数据" 
                               FontSize="16" 
                               Foreground="#999999"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="请尝试调整搜索条件或刷新数据" 
                               FontSize="12" 
                               Foreground="#CCCCCC"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <!-- 产品数据表格 -->
                <DataGrid x:Name="ProductDataGrid"
                          ItemsSource="{Binding ProductItems}"
                          SelectedItem="{Binding SelectedProduct}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          SelectionMode="Single"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          AlternatingRowBackground="#F8F9FA">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="序号" Width="60" Binding="{Binding Index}"/>
                        <DataGridTextColumn Header="产品编号" Binding="{Binding MaterialNumber}" Width="120"/>
                        <DataGridTextColumn Header="产品名称" Binding="{Binding MaterialName}" Width="150"/>
                        <DataGridTextColumn Header="规格型号" Binding="{Binding SpecificationModel}" Width="120"/>
                        <DataGridTextColumn Header="单位" Binding="{Binding Unit}" Width="60"/>
                        <DataGridTextColumn Header="产品类型" Binding="{Binding MaterialType}" Width="80"/>
                        <DataGridTextColumn Header="产品属性" Binding="{Binding MaterialProperty}" Width="80"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- 分页和按钮区域 -->
        <Grid Grid.Row="2" Margin="0,15,0,0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 分页控件 -->
            <Grid Grid.Row="0" Margin="0,0,0,15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 分页信息 -->
                <TextBlock Grid.Column="0"
                           Text="{Binding PageInfo}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="#666"/>

                <!-- 分页按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <Button Command="{Binding FirstPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="首页"
                            Height="28"
                            Width="50"
                            Margin="0,0,5,0"
                            FontSize="10"/>

                    <Button Command="{Binding PreviousPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="上一页"
                            Height="28"
                            Width="60"
                            Margin="0,0,5,0"
                            FontSize="10"/>

                    <Button Command="{Binding NextPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="下一页"
                            Height="28"
                            Width="60"
                            Margin="0,0,5,0"
                            FontSize="10"/>

                    <Button Command="{Binding LastPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="末页"
                            Height="28"
                            Width="50"
                            FontSize="10"/>
                </StackPanel>
            </Grid>

            <!-- 操作按钮 -->
            <StackPanel Grid.Row="1" 
                        Orientation="Horizontal" 
                        HorizontalAlignment="Right">
                
                <Button Style="{StaticResource MaterialDesignOutlinedButton}"
                        Content="取消"
                        Width="80"
                        Height="35"
                        Margin="0,0,10,0"
                        Click="CancelButton_Click"/>
                
                <Button Style="{StaticResource MaterialDesignRaisedButton}"
                        Content="确定"
                        Width="80"
                        Height="35"
                        Click="ConfirmButton_Click"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>