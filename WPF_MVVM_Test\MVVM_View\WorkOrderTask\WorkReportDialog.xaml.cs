using System.Windows;
using WPF_MVVM_Test.MVVM_Model.WorkOrderTask;
using WPF_MVVM_Test.MVVM_ViewModel.WorkOrderTask;

namespace WPF_MVVM_Test.MVVM_View.WorkOrderTask
{
    /// <summary>
    /// WorkReportDialog.xaml 的交互逻辑
    /// </summary>
    public partial class WorkReportDialog : Window
    {
        public WorkReportDialogViewModel ViewModel { get; private set; }

        public WorkReportDialog(WorkOrderTaskModel task)
        {
            InitializeComponent();
            ViewModel = new WorkReportDialogViewModel(task);
            DataContext = ViewModel;

            // 订阅ViewModel的属性变化事件
            ViewModel.PropertyChanged += ViewModel_PropertyChanged;
        }

        private void ViewModel_PropertyChanged(object sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(WorkReportDialogViewModel.DialogResult))
            {
                DialogResult = ViewModel.DialogResult;
            }
        }

        /// <summary>
        /// 获取报工请求数据
        /// </summary>
        /// <returns></returns>
        public WorkReportRequest GetWorkReportRequest()
        {
            return ViewModel.CreateWorkReportRequest();
        }
    }
}
