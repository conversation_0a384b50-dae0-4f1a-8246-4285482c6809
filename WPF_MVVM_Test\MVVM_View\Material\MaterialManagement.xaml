<UserControl x:Class="WPF_MVVM_Test.MVVM_View.Material.MaterialManagement"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:vm="clr-namespace:WPF_MVVM_Test.MVVM_ViewModel.Material"
             xmlns:local="clr-namespace:WPF_MVVM_Test.MVVM_View.Material"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.DataContext>
        <vm:MaterialManagementViewModel/>
    </UserControl.DataContext>

    <UserControl.Resources>
        <!-- 行索引转换器 -->
        <local:RowIndexConverter x:Key="RowIndexConverter"/>
        
        <!-- 库存状态转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 状态颜色转换器 -->
        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Green"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Status}" Value="停用">
                    <Setter Property="Foreground" Value="Red"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- 库存预警样式 -->
        <Style x:Key="StockTextStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="Black"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsLowStock}" Value="True">
                    <Setter Property="Foreground" Value="Red"/>
                    <Setter Property="FontWeight" Value="Bold"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <!-- 搜索区域 -->
            <RowDefinition Height="*"/>
            <!-- 数据表格区域 -->
            <RowDefinition Height="Auto"/>
            <!-- 分页区域 -->
        </Grid.RowDefinitions>

        <!-- 搜索区域 -->
        <materialDesign:Card Grid.Row="0" Margin="10" Padding="15">
            <StackPanel>
                <TextBlock Text="物料管理" 
                           FontSize="20" 
                           FontWeight="Bold" 
                           Margin="0,0,0,15"
                           Foreground="#2E3440"/>

                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- 物料名称搜索 -->
                    <TextBox Grid.Column="0"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="物料名称"
                             Text="{Binding MaterialNameFilter, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="60"/>

                    <!-- 物料编号搜索 -->
                    <TextBox Grid.Column="1"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="物料编号"
                             Text="{Binding MaterialNumberFilter, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="60"/>

                    <!-- 分类搜索 -->
                    <TextBox Grid.Column="2"
                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                             materialDesign:HintAssist.Hint="物料分类"
                             Text="{Binding CategoryFilter, UpdateSourceTrigger=PropertyChanged}"
                             Margin="0,0,10,0"
                             Height="60"/>

                    <!-- 搜索按钮 -->
                    <Button Grid.Column="3"
                            Command="{Binding SearchCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="搜索"
                            Height="40"
                            Width="70"
                            Margin="0,0,10,0"/>

                    <!-- 刷新按钮 -->
                    <Button Grid.Column="4"
                            Command="{Binding RefreshCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="刷新"
                            Height="40"
                            Width="70"
                            Margin="0,0,10,0"/>

                    <!-- 新增按钮 -->
                    <Button Grid.Column="5"
                            Command="{Binding AddMaterialCommand}"
                            Style="{StaticResource MaterialDesignRaisedButton}"
                            Content="新增"
                            Height="40"
                            Width="70"/>


                </Grid>
            </StackPanel>
        </materialDesign:Card>

        <!-- 数据表格区域 -->
        <materialDesign:Card Grid.Row="1" Margin="10,0,10,10">
            <Grid>
                <!-- 加载指示器 -->
                <ProgressBar IsIndeterminate="True" 
                             VerticalAlignment="Top"
                             Height="4">
                    <ProgressBar.Style>
                        <Style TargetType="ProgressBar">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding IsLoading}" Value="True">
                                    <Setter Property="Visibility" Value="Visible"/>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ProgressBar.Style>
                </ProgressBar>

                <!-- 无数据提示 -->
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <StackPanel.Style>
                        <Style TargetType="StackPanel">
                            <Setter Property="Visibility" Value="Collapsed"/>
                            <Style.Triggers>
                                <MultiDataTrigger>
                                    <MultiDataTrigger.Conditions>
                                        <Condition Binding="{Binding IsLoading}" Value="False"/>
                                        <Condition Binding="{Binding MaterialItems.Count}" Value="0"/>
                                    </MultiDataTrigger.Conditions>
                                    <Setter Property="Visibility" Value="Visible"/>
                                </MultiDataTrigger>
                            </Style.Triggers>
                        </Style>
                    </StackPanel.Style>

                    <materialDesign:PackIcon Kind="PackageVariant" 
                                             Width="64" 
                                             Height="64" 
                                             Foreground="#CCCCCC"
                                             Margin="0,0,0,20"/>
                    <TextBlock Text="暂无物料数据" 
                               FontSize="16" 
                               Foreground="#999999"
                               HorizontalAlignment="Center"/>
                    <TextBlock Text="请尝试调整搜索条件或刷新数据" 
                               FontSize="12" 
                               Foreground="#CCCCCC"
                               HorizontalAlignment="Center"
                               Margin="0,5,0,0"/>
                </StackPanel>

                <!-- 数据表格 -->
                <DataGrid ItemsSource="{Binding MaterialItems}"
                          AutoGenerateColumns="False"
                          IsReadOnly="True"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F8F9FA"
                          FontSize="12">

                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="序号" Width="60">
                            <DataGridTextColumn.Binding>
                                <Binding RelativeSource="{RelativeSource AncestorType=DataGridRow}" 
                                         Converter="{StaticResource RowIndexConverter}"/>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>

                        <!-- 物料图片 -->
                        <DataGridTemplateColumn Header="图片" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border Width="60" Height="60" 
                                            Background="#F0F0F0" 
                                            CornerRadius="4"
                                            Margin="5">
                                        <Grid>
                                            <!-- 默认图标 -->
                                            <materialDesign:PackIcon Kind="PackageVariant" 
                                                                     Width="30" 
                                                                     Height="30" 
                                                                     Foreground="#CCCCCC"
                                                                     HorizontalAlignment="Center"
                                                                     VerticalAlignment="Center"/>
                                            <!-- 实际图片（如果有的话） -->
                                            <Image Source="{Binding MaterialImage}" 
                                                   Stretch="UniformToFill"
                                                   Visibility="Collapsed"/>
                                        </Grid>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 物料编号 -->
                        <DataGridTextColumn Header="物料编号" 
                                            Binding="{Binding MaterialNumber}" 
                                            Width="120"/>

                        <!-- 物料名称 -->
                        <DataGridTextColumn Header="物料名称" 
                                            Binding="{Binding MaterialName}" 
                                            Width="150"/>

                        <!-- 规格型号 -->
                        <DataGridTextColumn Header="规格型号" 
                                            Binding="{Binding SpecificationModel}" 
                                            Width="120"/>

                        <!-- 单位 -->
                        <DataGridTextColumn Header="单位" 
                                            Binding="{Binding Unit}" 
                                            Width="60"/>

                        <!-- 物料类型 -->
                        <DataGridTextColumn Header="物料类型" 
                                            Binding="{Binding MaterialType}" 
                                            Width="80"/>

                        <!-- 物料属性 -->
                        <DataGridTextColumn Header="物料属性" 
                                            Binding="{Binding MaterialProperty}" 
                                            Width="80"/>

                        <!-- 物料分类 -->
                        <DataGridTextColumn Header="物料分类" 
                                            Binding="{Binding MaterialCategoryName}" 
                                            Width="120"/>

                        <!-- 状态 -->
                        <DataGridTemplateColumn Header="状态" Width="60">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Status}" 
                                               Style="{StaticResource StatusTextStyle}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 操作列 -->
                        <DataGridTemplateColumn Header="操作" Width="80">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- 编辑按钮 -->
                                        <Button Command="{Binding DataContext.EditMaterialCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                CommandParameter="{Binding}"
                                                Style="{StaticResource MaterialDesignRaisedButton}"
                                                Content="编辑"
                                                Height="28"
                                                Width="50"
                                                FontSize="10"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </materialDesign:Card>

        <!-- 分页区域 -->
        <materialDesign:Card Grid.Row="2" Margin="10,0,10,10" Padding="15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 分页信息 -->
                <TextBlock Grid.Column="0"
                           Text="{Binding PageInfo}"
                           VerticalAlignment="Center"
                           FontSize="12"
                           Foreground="#666"/>

                <!-- 分页控件 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <!-- 首页按钮 -->
                    <Button Command="{Binding FirstPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="首页"
                            Height="32"
                            Width="60"
                            Margin="0,0,5,0"
                            FontSize="11"/>

                    <!-- 上一页按钮 -->
                    <Button Command="{Binding PreviousPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="上一页"
                            Height="32"
                            Width="70"
                            Margin="0,0,5,0"
                            FontSize="11"/>

                    <TextBox Text="{Binding JumpToPage, UpdateSourceTrigger=PropertyChanged}"
                             Width="40"
                             Height="32"
                             FontSize="11"
                             TextAlignment="Center"
                             VerticalContentAlignment="Center"
                             Margin="0,0,5,0">
                        <TextBox.InputBindings>
                            <KeyBinding Key="Enter" Command="{Binding GoToPageCommand}"/>
                        </TextBox.InputBindings>
                    </TextBox>
                    <TextBlock Text="页" 
                               VerticalAlignment="Center" 
                               FontSize="11" 
                               Margin="0,0,5,0"/>

                    <!-- 下一页按钮 -->
                    <Button Command="{Binding NextPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="下一页"
                            Height="32"
                            Width="60"
                            Margin="5,0,5,0"
                            FontSize="11"/>

                    <!-- 末页按钮 -->
                    <Button Command="{Binding LastPageCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Content="末页"
                            Height="32"
                            Width="50"
                            Margin="0,0,0,0"
                            FontSize="11"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>