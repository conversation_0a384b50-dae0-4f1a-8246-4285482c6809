using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using WPF_MVVM_Test.MVVM_Model.Plan;
using WPF_MVVM_Test.Services.Plan;

namespace WPF_MVVM_Test.MVVM_ViewModel.Plan
{
    public class SelectBomViewModel : BaseViewModel
    {
        private ProductBomInfo _productInfo = new();
        private ObservableCollection<BomModel> _bomList = new();
        private BomModel? _selectedBom;
        private int _currentPage = 1;
        private int _totalCount = 0;
        private readonly int _pageSize = 10;
        private string _productNameFilter = string.Empty;
        private string _bomNumberFilter = string.Empty;
        private string _jumpPage = "";

        public ProductBomInfo ProductInfo
        {
            get => _productInfo;
            set => SetProperty(ref _productInfo, value);
        }

        public ObservableCollection<BomModel> BomList
        {
            get => _bomList;
            set => SetProperty(ref _bomList, value);
        }

        public BomModel? SelectedBom
        {
            get => _selectedBom;
            set => SetProperty(ref _selectedBom, value);
        }

        public int CurrentPage
        {
            get => _currentPage;
            set => SetProperty(ref _currentPage, value);
        }

        public int TotalCount
        {
            get => _totalCount;
            set => SetProperty(ref _totalCount, value);
        }

        public int TotalPage => (int)Math.Ceiling((double)TotalCount / _pageSize);

        public string PageInfo => $"共 {TotalCount} 条";

        public bool CanGoPreviousPage => CurrentPage > 1;
        public bool CanGoNextPage => CurrentPage < TotalPage;

        public ICommand PreviousPageCommand { get; }
        public ICommand NextPageCommand { get; }
        public ICommand GoToPageCommand { get; }
        public ICommand CancelCommand { get; }
        public ICommand ConfirmCommand { get; }
        public ICommand SearchCommand { get; }

        public string ProductNameFilter
        {
            get => _productNameFilter;
            set => SetProperty(ref _productNameFilter, value);
        }
        public string BomNumberFilter
        {
            get => _bomNumberFilter;
            set => SetProperty(ref _bomNumberFilter, value);
        }

        public string JumpPage
        {
            get => _jumpPage;
            set => SetProperty(ref _jumpPage, value);
        }

        public SelectBomViewModel(ProductBomInfo productInfo)
        {
            ProductInfo = productInfo;
            
            PreviousPageCommand = CreateCommand(PreviousPage);
            NextPageCommand = CreateCommand(NextPage);
            GoToPageCommand = CreateCommand<string>(GoToPage);
            CancelCommand = CreateCommand(Cancel);
            ConfirmCommand = CreateCommand(Confirm);
            SearchCommand = CreateCommand(Search);

            LoadBomData();
        }

        private async void LoadBomData()
        {
            using var service = new BomService();
            var (boms, totalCount, totalPage) = await service.GetBomsAsync(CurrentPage, _pageSize, ProductNameFilter, BomNumberFilter);
            BomList = new ObservableCollection<BomModel>(boms);
            TotalCount = totalCount;
            OnPropertyChanged(nameof(TotalPage));
            OnPropertyChanged(nameof(PageInfo));
            OnPropertyChanged(nameof(CanGoPreviousPage));
            OnPropertyChanged(nameof(CanGoNextPage));
        }

        private void PreviousPage()
        {
            if (CanGoPreviousPage)
            {
                CurrentPage--;
                LoadBomData();
            }
        }

        private void NextPage()
        {
            if (CanGoNextPage)
            {
                CurrentPage++;
                LoadBomData();
            }
        }

        private void GoToPage(string pageStr)
        {
            if (int.TryParse(pageStr, out int page) && page >= 1 && page <= TotalPage)
            {
                CurrentPage = page;
                LoadBomData();
            }
            JumpPage = string.Empty;
        }

        private void Cancel()
        {
            // 关闭对话框并返回false
            foreach (System.Windows.Window window in System.Windows.Application.Current.Windows)
            {
                if (window is WPF_MVVM_Test.MVVM_View.Dialog.SelectBomDialog dialog)
                {
                    dialog.DialogResult = false;
                    dialog.Close();
                    break;
                }
            }
        }

        private void Confirm()
        {
            if (SelectedBom == null)
            {
                System.Windows.MessageBox.Show("请选择一个BOM！", "提示",
                    System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Warning);
                return;
            }

            // 关闭对话框并返回结果
            foreach (System.Windows.Window window in System.Windows.Application.Current.Windows)
            {
                if (window is WPF_MVVM_Test.MVVM_View.Dialog.SelectBomDialog dialog)
                {
                    dialog.DialogResult = true;
                    dialog.Close();
                    break;
                }
            }
        }

        private void Search()
        {
            CurrentPage = 1;
            LoadBomData();
        }
    }
}