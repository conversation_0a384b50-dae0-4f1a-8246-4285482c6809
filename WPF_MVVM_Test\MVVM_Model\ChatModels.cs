using System;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model
{
    public class ChatMessage
    {
        public string Content { get; set; } = string.Empty;
        public bool IsUser { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    public class ChatRequest
    {
        [JsonPropertyName("content")]
        public string content { get; set; } = string.Empty;
        
        [JsonPropertyName("botId")]
        public string botId { get; set; } = "7524702072735367168";
        
        [JsonPropertyName("userId")]
        public string userId { get; set; } = "123";
        
        [JsonPropertyName("conversationId")]
        public string conversationId { get; set; } = "123";
        
        [JsonPropertyName("stream")]
        public bool stream { get; set; } = false;
        
        [JsonPropertyName("autoSaveHistory")]
        public bool autoSaveHistory { get; set; } = true;
    }
    
    public class ChatResponse
    {
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;
        
        [JsonPropertyName("conversationId")]
        public string ConversationId { get; set; } = string.Empty;
        
        [JsonPropertyName("success")]
        public bool Success { get; set; }
        
        [JsonPropertyName("errorMessage")]
        public string ErrorMessage { get; set; } = string.Empty;
        
        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; }
        
        [JsonPropertyName("tokensUsed")]
        public int TokensUsed { get; set; }
    }
}