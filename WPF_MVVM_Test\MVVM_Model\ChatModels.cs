using System;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model
{
    public class ChatMessage
    {
        public string Content { get; set; } = string.Empty;
        public bool IsUser { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    public class ChatRequest
    {
        [JsonPropertyName("content")]
        public string content { get; set; } = string.Empty;

        [JsonPropertyName("botId")]
        public string botId { get; set; } = "7524702072735367168";

        [JsonPropertyName("userId")]
        public string userId { get; set; } = "123";

        [JsonPropertyName("conversationId")]
        public string conversationId { get; set; } = "123";

        [JsonPropertyName("stream")]
        public bool stream { get; set; } = false;

        [JsonPropertyName("autoSaveHistory")]
        public bool autoSaveHistory { get; set; } = true;
    }

    public class ChatResponse
    {
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("conversationId")]
        public string ConversationId { get; set; } = string.Empty;

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("errorMessage")]
        public string ErrorMessage { get; set; } = string.Empty;

        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; }

        [JsonPropertyName("tokensUsed")]
        public int TokensUsed { get; set; }
    }

    // MES AI 相关模型
    public class MesAiRequest
    {
        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;
    }

    // MES AI 响应模型
    public class MesAiResponse
    {
        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [JsonPropertyName("content")]
        public string Content { get; set; } = string.Empty;

        [JsonPropertyName("sessionId")]
        public string? SessionId { get; set; }

        [JsonPropertyName("errorMessage")]
        public string? ErrorMessage { get; set; }

        [JsonPropertyName("modelName")]
        public string? ModelName { get; set; }

        [JsonPropertyName("processingTimeMs")]
        public int ProcessingTimeMs { get; set; }

        [JsonPropertyName("tokenUsage")]
        public object? TokenUsage { get; set; }
    }


}