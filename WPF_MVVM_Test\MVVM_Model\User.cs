using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text.Json.Serialization;

namespace WPF_MVVM_Test.MVVM_Model
{
    /// <summary>
    /// 用户数据模型 - 根据后台API响应字段定义
    /// </summary>
    public class User : INotifyPropertyChanged
    {
        private string _id = string.Empty;
        private List<string> _roleName = new List<string>();
        private string _userName = string.Empty;
        private string _userEmail = string.Empty;
        private string _userPhone = string.Empty;
        private bool? _userSex;

        /// <summary>
        /// 用户ID
        /// </summary>
        [JsonPropertyName("id")]
        public string Id
        {
            get => _id;
            set
            {
                _id = value ?? string.Empty;
                OnPropertyChanged(nameof(Id));
            }
        }

        /// <summary>
        /// 角色名称列表
        /// </summary>
        [JsonPropertyName("roleName")]
        public List<string> RoleName
        {
            get => _roleName;
            set
            {
                _roleName = value ?? new List<string>();
                OnPropertyChanged(nameof(RoleName));
                OnPropertyChanged(nameof(RoleNameText));
            }
        }

        /// <summary>
        /// 角色名称文本 - 用于界面显示（多个角色用逗号分隔）
        /// </summary>
        public string RoleNameText => RoleName != null && RoleName.Any()
            ? string.Join(", ", RoleName)
            : "无角色";

        /// <summary>
        /// 用户名
        /// </summary>
        [JsonPropertyName("userName")]
        public string UserName
        {
            get => _userName;
            set
            {
                _userName = value ?? string.Empty;
                OnPropertyChanged(nameof(UserName));
            }
        }

        /// <summary>
        /// 用户邮箱
        /// </summary>
        [JsonPropertyName("userEmail")]
        public string UserEmail
        {
            get => _userEmail;
            set
            {
                _userEmail = value ?? string.Empty;
                OnPropertyChanged(nameof(UserEmail));
            }
        }

        /// <summary>
        /// 用户手机号
        /// </summary>
        [JsonPropertyName("userPhone")]
        public string UserPhone
        {
            get => _userPhone;
            set
            {
                _userPhone = value ?? string.Empty;
                OnPropertyChanged(nameof(UserPhone));
            }
        }

        /// <summary>
        /// 用户性别 - 可为空 (true=男, false=女, null=未设置)
        /// </summary>
        [JsonPropertyName("userSex")]
        public bool? UserSex
        {
            get => _userSex;
            set
            {
                _userSex = value;
                OnPropertyChanged(nameof(UserSex));
                OnPropertyChanged(nameof(UserSexText));
            }
        }

        /// <summary>
        /// 性别显示文本
        /// </summary>
        public string UserSexText => UserSex switch
        {
            true => "男",
            false => "女",
            null => "未设置"
        };

        /// <summary>
        /// 属性变更通知事件
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// 触发属性变更通知
        /// </summary>
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public User()
        {
            RoleName = new List<string>();
        }

        /// <summary>
        /// 带参数的构造函数
        /// </summary>
        public User(string id, string userName, string userEmail, string userPhone) : this()
        {
            Id = id;
            UserName = userName;
            UserEmail = userEmail;
            UserPhone = userPhone;
        }
    }
}
